# 🎖️ Système de Contrôle d'Accès Militaire

## 📋 Description

Système de contrôle d'accès pour site militaire gérant l'accès de :
- **Militaires internes** : badges permanents
- **Militaires externes** : badges visiteurs réutilisables  
- **Civils visiteurs** : badges visiteurs réutilisables

## 🏗️ Architecture

```
controle-acces/
├── server/          # Backend Express.js + PostgreSQL
├── client/          # Frontend React
├── database/        # Scripts SQL et migrations
└── docs/           # Documentation
```

## 🚀 Installation

```bash
# Installation de toutes les dépendances
npm run install:all

# Démarrage en mode développement
npm run dev
```

## 🔧 Configuration

1. **Base de données PostgreSQL**
   - Créer une base de données `controle_acces`
   - Configurer les variables d'environnement dans `server/.env`

2. **Variables d'environnement**
   ```
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=controle_acces
   DB_USER=your_user
   DB_PASSWORD=your_password
   JWT_SECRET=your_jwt_secret
   PORT=3001
   ```

## 📊 Fonctionnalités

### 👥 Gestion du Personnel
- Création de profils militaires internes/externes
- Gestion des profils civils visiteurs
- Formulaires adaptés par type d'utilisateur

### 🏷️ Gestion des Badges
- Attribution automatique (militaires internes)
- Attribution manuelle (visiteurs)
- Réutilisation des badges visiteurs
- Suivi des disponibilités

### 📈 Monitoring des Accès
- Enregistrement temps réel des passages
- Historique complet des entrées/sorties
- Interface de monitoring en direct

## 🛠️ Technologies

- **Backend** : Express.js, PostgreSQL, Sequelize
- **Frontend** : React, Material-UI, Axios
- **Sécurité** : JWT, validation des données
- **Base de données** : PostgreSQL avec relations normalisées
