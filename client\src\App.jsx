// src/App.jsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import PersonnelManagement from './pages/PersonnelManagement';
import BadgeManagement from './pages/BadgeManagement';
import AccessLog from './pages/AccessLog';
import ActiveAssignments from './pages/ActiveAssignments';
import Statistics from './pages/Statistics';
import Settings from './pages/Settings';

function App() {
  return (
    <React.StrictMode>
      <AuthProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/personnel" element={<PersonnelManagement />} />
              <Route path="/badges" element={<BadgeManagement />} />
              <Route path="/journal" element={<AccessLog />} />
              <Route path="/attributions" element={<ActiveAssignments />} />
              <Route path="/statistiques" element={<Statistics />} />
              <Route path="/parametres" element={<Settings />} />
            </Routes>
          </Layout>
        </Router>
      </AuthProvider>
    </React.StrictMode>
  );
}

export default App;
