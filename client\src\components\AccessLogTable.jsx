export function AccessLogTable({ passages }) {
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <h2 className="text-lg font-semibold mb-3">Journal des accès</h2>
      <table className="w-full text-sm">
        <thead>
          <tr className="text-left text-gray-500 border-b">
            <th className="py-2">Porte</th>
            <th className="py-2">Badge</th>
            <th className="py-2">Heure</th>
            <th className="py-2">Résultat</th>
          </tr>
        </thead>
        <tbody>
          {passages.slice(0, 5).map((p, i) => (
            <tr key={i} className="border-b">
              <td className="py-2">{p.porte}</td>
              <td className="py-2">{p.badge}</td>
              <td className="py-2">{new Date(p.date_acces).toLocaleTimeString()}</td>
              <td className="py-2">{p.resultat}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}