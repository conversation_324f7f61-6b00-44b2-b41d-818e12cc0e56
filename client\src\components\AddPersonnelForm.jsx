import { useState } from 'react';
import api from '../services/api';

export function AddPersonnelForm({ onSuccess }) {
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    id_type_personnel: '',
    id_grade: '',
    id_unite: ''
  });

  const handleChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async e => {
    e.preventDefault();
    try {
      await api.post('/personnels', formData);
      if (onSuccess) onSuccess();
    } catch (err) {
      console.error('Erreur lors de l’ajout :', err);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow mb-6">
      <h2 className="text-lg font-semibold mb-4">Ajouter un personnel</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm text-gray-700">Nom</label>
          <input name="nom" value={formData.nom} onChange={handleChange} required className="mt-1 w-full px-3 py-2 border rounded" />
        </div>
        <div>
          <label className="block text-sm text-gray-700">Prénom</label>
          <input name="prenom" value={formData.prenom} onChange={handleChange} required className="mt-1 w-full px-3 py-2 border rounded" />
        </div>
        <div>
          <label className="block text-sm text-gray-700">Type Personnel</label>
          <input name="id_type_personnel" value={formData.id_type_personnel} onChange={handleChange} className="mt-1 w-full px-3 py-2 border rounded" />
        </div>
        <div>
          <label className="block text-sm text-gray-700">Grade</label>
          <input name="id_grade" value={formData.id_grade} onChange={handleChange} className="mt-1 w-full px-3 py-2 border rounded" />
        </div>
        <div>
          <label className="block text-sm text-gray-700">Unité</label>
          <input name="id_unite" value={formData.id_unite} onChange={handleChange} className="mt-1 w-full px-3 py-2 border rounded" />
        </div>
      </div>
      <div className="mt-4">
        <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Ajouter</button>
      </div>
    </form>
  );
}
