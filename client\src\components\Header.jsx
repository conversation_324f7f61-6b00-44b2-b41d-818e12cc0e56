import React, { useState, useEffect } from 'react';
import { LogOut, User, Clock } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { ReadOnlyBadge } from './ProtectedAction';

export function Header() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const { user, logout } = useAuth();

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleLogout = () => {
    logout();
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-semibold text-gray-800">
            Système de Contrôle d'Accès
          </h1>
          <ReadOnlyBadge />
        </div>
        
        <div className="flex items-center space-x-6">
          {/* Affichage de l'heure */}
          <div className="flex items-center space-x-2 text-gray-600">
            <Clock className="h-4 w-4" />
            <div className="text-sm">
              <div className="font-medium">{formatTime(currentTime)}</div>
              <div className="text-xs text-gray-500">{formatDate(currentTime)}</div>
            </div>
          </div>
          
          {/* Informations agent */}
          <div className="flex items-center space-x-2 text-gray-600">
            <User className="h-4 w-4" />
            <div className="text-sm">
              <div className="font-medium">{user?.prenom} {user?.nom}</div>
              <div className="text-xs text-gray-500 capitalize">{user?.role}</div>
            </div>
          </div>
          
          {/* Bouton déconnexion */}
          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
          >
            <LogOut className="h-4 w-4" />
            <span>Déconnexion</span>
          </button>
        </div>
      </div>
    </header>
  );
}
