import { useAuth } from '../contexts/AuthContext';
import { Alert } from './ui';

export function ProtectedAction({ 
  children, 
  permission = 'write', 
  fallback = null, 
  showAlert = false 
}) {
  const { hasPermission, isReadOnly } = useAuth();

  if (!hasPermission(permission)) {
    if (showAlert) {
      return (
        <Alert variant="warning">
          Vous n'avez pas les permissions nécessaires pour effectuer cette action.
        </Alert>
      );
    }
    return fallback;
  }

  return children;
}

export function ReadOnlyBadge() {
  const { isReadOnly } = useAuth();

  if (!isReadOnly()) return null;

  return (
    <div className="bg-yellow-100 border border-yellow-300 text-yellow-800 px-3 py-1 rounded-full text-xs font-medium">
      Mode Lecture Seule
    </div>
  );
}
