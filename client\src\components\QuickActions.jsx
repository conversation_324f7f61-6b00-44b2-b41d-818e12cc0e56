import React from 'react';
import { useNavigate } from 'react-router-dom';
import { UserPlus, CreditCard, Clock, Users } from 'lucide-react';

export function QuickActions({ onSuccess }) {
  const navigate = useNavigate();

  const quickActions = [
    {
      icon: UserPlus,
      title: 'Nouveau Personnel',
      description: 'Ajouter un militaire ou civil',
      action: () => navigate('/personnel'),
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      icon: CreditCard,
      title: 'Clôturer Badge Visiteur',
      description: 'Terminer une visite',
      action: () => navigate('/attributions'),
      color: 'bg-orange-500 hover:bg-orange-600'
    },
    {
      icon: Clock,
      title: 'Journal des Passages',
      description: 'Voir l\'historique récent',
      action: () => navigate('/journal'),
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      icon: Users,
      title: 'Attributions Actives',
      description: 'Badges en cours d\'utilisation',
      action: () => navigate('/attributions'),
      color: 'bg-purple-500 hover:bg-purple-600'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">Actions Rapides</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickActions.map((action, index) => {
          const Icon = action.icon;
          return (
            <button
              key={index}
              onClick={action.action}
              className={`${action.color} text-white p-4 rounded-lg transition-colors flex flex-col items-center text-center space-y-2`}
            >
              <Icon className="h-8 w-8" />
              <div>
                <div className="font-medium text-sm">{action.title}</div>
                <div className="text-xs opacity-90">{action.description}</div>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
}
