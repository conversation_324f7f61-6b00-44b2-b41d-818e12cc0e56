import { createContext, useContext, useState, useEffect } from 'react';
import api from '../services/api';

const AuthContext = createContext();

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simuler une vérification d'authentification
    // En production, ceci devrait vérifier un token JWT ou session
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        if (token) {
          // Vérifier le token avec l'API
          const response = await api.get('/auth/verify', {
            headers: { Authorization: `Bearer ${token}` }
          });
          setUser(response.data.user);
        } else {
          // Pour la démo, créer un utilisateur par défaut
          setUser({
            id: 1,
            nom: 'Agent',
            prenom: 'de Service',
            role: 'admin', // 'admin' ou 'user'
            permissions: ['read', 'write', 'delete'] // 'read' pour lecture seule
          });
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        // Pour la démo, créer un utilisateur par défaut
        setUser({
          id: 1,
          nom: 'Agent',
          prenom: 'de Service',
          role: 'admin',
          permissions: ['read', 'write', 'delete']
        });
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (credentials) => {
    try {
      const response = await api.post('/auth/login', credentials);
      const { token, user } = response.data;
      
      localStorage.setItem('auth_token', token);
      setUser(user);
      
      // Configurer l'intercepteur axios pour inclure le token
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Erreur de connexion' 
      };
    }
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    delete api.defaults.headers.common['Authorization'];
    setUser(null);
  };

  const hasPermission = (permission) => {
    return user?.permissions?.includes(permission) || false;
  };

  const isAdmin = () => {
    return user?.role === 'admin';
  };

  const isReadOnly = () => {
    return user?.role === 'user' || !hasPermission('write');
  };

  const value = {
    user,
    loading,
    login,
    logout,
    hasPermission,
    isAdmin,
    isReadOnly
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
