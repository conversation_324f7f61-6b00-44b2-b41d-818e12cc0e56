import { useState, useEffect } from 'react';
import { User<PERSON><PERSON><PERSON>, Clock, AlertTriangle, CheckCircle, X } from 'lucide-react';
import api from '../services/api';
import { Card, CardHeader, CardContent, CardTitle, DataTable, Alert, Modal } from '../components/ui';

function ActiveAssignments() {
  const [attributions, setAttributions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [selectedAttribution, setSelectedAttribution] = useState(null);
  const [filterType, setFilterType] = useState('all');

  const fetchAttributions = async () => {
    try {
      setLoading(true);
      const response = await api.get('/badges/attributions-actives');
      setAttributions(response.data);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des attributions');
      console.error('Error fetching attributions:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAttributions();

    // Auto-refresh toutes les 5 secondes
    const interval = setInterval(fetchAttributions, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleCloseAttribution = (attribution) => {
    setSelectedAttribution(attribution);
    setShowCloseModal(true);
  };

  const confirmCloseAttribution = async () => {
    if (!selectedAttribution) return;

    try {
      await api.patch(`/badges/${selectedAttribution.badge_id}/cloturer`);
      setSuccess('Attribution clôturée avec succès');
      setShowCloseModal(false);
      setSelectedAttribution(null);
      fetchAttributions();
    } catch (err) {
      setError('Erreur lors de la clôture de l\'attribution');
    }
  };

  const getDurationString = (dateAttribution) => {
    const now = new Date();
    const start = new Date(dateAttribution);
    const diffMs = now - start;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`;
    }
    return `${diffMinutes}m`;
  };

  const isLongDuration = (dateAttribution, type) => {
    const now = new Date();
    const start = new Date(dateAttribution);
    const diffHours = (now - start) / (1000 * 60 * 60);

    // Alerte si visiteur depuis plus de 8h ou militaire interne depuis plus de 24h
    if (type === 'visiteur' && diffHours > 8) return true;
    if (type === 'militaire_interne' && diffHours > 24) return true;
    return false;
  };

  const filteredAttributions = attributions.filter(attribution => {
    if (filterType === 'all') return true;
    if (filterType === 'visiteurs') return attribution.badge_type === 'visiteur';
    if (filterType === 'militaires') return attribution.badge_type === 'militaire_interne';
    if (filterType === 'alertes') return isLongDuration(attribution.date_attribution, attribution.badge_type);
    return true;
  });

  const columns = [
    {
      header: 'Badge',
      accessor: 'badge_numero',
      render: (attribution) => (
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            attribution.badge_type === 'visiteur' ? 'bg-orange-400' : 'bg-blue-400'
          }`} />
          <span className="font-mono font-medium">#{attribution.badge_numero}</span>
        </div>
      )
    },
    {
      header: 'Personnel',
      render: (attribution) => (
        <div>
          <div className="font-medium text-gray-900">
            {attribution.personnel_nom} {attribution.personnel_prenom}
          </div>
          <div className="text-sm text-gray-500">
            {attribution.personnel_matricule || attribution.personnel_cin}
          </div>
        </div>
      )
    },
    {
      header: 'Type',
      accessor: 'personnel_type',
      render: (attribution) => {
        const typeLabels = {
          militaire_interne: 'Militaire Interne',
          militaire_externe: 'Militaire Externe',
          civil_externe: 'Civil Externe'
        };
        const typeColors = {
          militaire_interne: 'bg-blue-100 text-blue-800',
          militaire_externe: 'bg-green-100 text-green-800',
          civil_externe: 'bg-purple-100 text-purple-800'
        };
        return (
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${typeColors[attribution.personnel_type]}`}>
            {typeLabels[attribution.personnel_type]}
          </span>
        );
      }
    },
    {
      header: 'Date Attribution',
      accessor: 'date_attribution',
      render: (attribution) => (
        <div>
          <div className="font-medium text-gray-900">
            {new Date(attribution.date_attribution).toLocaleDateString('fr-FR')}
          </div>
          <div className="text-sm text-gray-500">
            {new Date(attribution.date_attribution).toLocaleTimeString('fr-FR')}
          </div>
        </div>
      )
    },
    {
      header: 'Durée',
      render: (attribution) => {
        const duration = getDurationString(attribution.date_attribution);
        const isLong = isLongDuration(attribution.date_attribution, attribution.badge_type);

        return (
          <div className={`flex items-center space-x-1 ${isLong ? 'text-orange-600' : 'text-gray-600'}`}>
            <Clock className="h-4 w-4" />
            <span className="font-medium">{duration}</span>
            {isLong && <AlertTriangle className="h-4 w-4" />}
          </div>
        );
      }
    },
    {
      header: 'Destination',
      accessor: 'destination',
      render: (attribution) => attribution.destination || attribution.unite || '-'
    },
    {
      header: 'Actions',
      render: (attribution) => (
        <button
          onClick={() => handleCloseAttribution(attribution)}
          className="bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700 flex items-center space-x-1 text-sm"
        >
          <X className="h-3 w-3" />
          <span>Clôturer</span>
        </button>
      )
    }
  ];

  const attributionStats = {
    total: attributions.length,
    visiteurs: attributions.filter(a => a.badge_type === 'visiteur').length,
    militaires: attributions.filter(a => a.badge_type === 'militaire_interne').length,
    alertes: attributions.filter(a => isLongDuration(a.date_attribution, a.badge_type)).length
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Chargement...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Attributions Actives</h1>
        <div className="text-sm text-gray-500">
          Mise à jour automatique toutes les 5 secondes
        </div>
      </div>

      {error && (
        <Alert variant="error" className="mb-6" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-6" dismissible onDismiss={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Statistiques */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Actif</p>
                <p className="text-2xl font-bold text-gray-800">{attributionStats.total}</p>
              </div>
              <UserCheck className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Visiteurs</p>
                <p className="text-2xl font-bold text-orange-600">{attributionStats.visiteurs}</p>
              </div>
              <UserCheck className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Militaires</p>
                <p className="text-2xl font-bold text-blue-600">{attributionStats.militaires}</p>
              </div>
              <UserCheck className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Alertes</p>
                <p className="text-2xl font-bold text-red-600">{attributionStats.alertes}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Badges Actuellement Attribués</CardTitle>
            <div className="flex items-center space-x-4">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">Toutes les attributions</option>
                <option value="visiteurs">Badges visiteurs</option>
                <option value="militaires">Badges militaires</option>
                <option value="alertes">Alertes durée</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <DataTable
            data={filteredAttributions}
            columns={columns}
            searchable={true}
            sortable={true}
            pagination={true}
            pageSize={10}
          />
        </CardContent>
      </Card>

      {/* Modal de confirmation de clôture */}
      <Modal
        isOpen={showCloseModal}
        onClose={() => setShowCloseModal(false)}
        title="Clôturer Attribution"
        size="md"
      >
        {selectedAttribution && (
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <span className="font-medium text-yellow-800">Confirmation requise</span>
              </div>
              <p className="text-yellow-700 mt-2">
                Êtes-vous sûr de vouloir clôturer l'attribution du badge #{selectedAttribution.badge_numero}
                pour {selectedAttribution.personnel_nom} {selectedAttribution.personnel_prenom} ?
              </p>
            </div>

            <div className="bg-gray-50 rounded-md p-4">
              <h4 className="font-medium text-gray-800 mb-2">Détails de l'attribution :</h4>
              <div className="space-y-1 text-sm text-gray-600">
                <div>Badge : #{selectedAttribution.badge_numero}</div>
                <div>Personnel : {selectedAttribution.personnel_nom} {selectedAttribution.personnel_prenom}</div>
                <div>Type : {selectedAttribution.personnel_type}</div>
                <div>Durée : {getDurationString(selectedAttribution.date_attribution)}</div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <button
                onClick={() => setShowCloseModal(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={confirmCloseAttribution}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center space-x-2"
              >
                <CheckCircle className="h-4 w-4" />
                <span>Confirmer la clôture</span>
              </button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}

export default ActiveAssignments;
