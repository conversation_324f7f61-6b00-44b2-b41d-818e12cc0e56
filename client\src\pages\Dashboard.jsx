import { useState, useEffect } from 'react';
import api from "../services/api";
import { Sidebar } from "../components/Sidebar";
import { StatCards } from "../components/StatCards";
import { RealTimeHistory } from "../components/RealTimeHistory";
import { AccessLogTable } from "../components/AccessLogTable";
import { AddPersonnelForm } from "../components/AddPersonnelForm";

function Dashboard() {
  const [personnels, setPersonnels] = useState([]);
  const [passages, setPassages] = useState([]);
  const [badgesActifs, setBadgesActifs] = useState(0);

  const fetchData = () => {
    api.get('/personnels').then(res => setPersonnels(res.data));
    api.get('/passages').then(res => setPassages(res.data));
    api.get('/badges?statut=actif').then(res => setBadgesActifs(res.data.length));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="min-h-screen flex bg-gray-100">
      <Sidebar />
      <main className="flex-1 p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-semibold">Dashboard</h1>
        </div>
        <StatCards passages={passages.length} badgesActifs={badgesActifs} personnels={personnels.length} />
        <AddPersonnelForm onSuccess={fetchData} />
        <RealTimeHistory personnels={personnels} />
        <AccessLogTable passages={passages} />
      </main>
    </div>
  );
}

export default Dashboard;