import { useState, useEffect } from 'react';
import api from "../services/api";
import { StatCards } from "../components/StatCards";
import { RealTimeHistory } from "../components/RealTimeHistory";
import { AccessLogTable } from "../components/AccessLogTable";
import { QuickActions } from "../components/QuickActions";

function Dashboard() {
  const [personnels, setPersonnels] = useState([]);
  const [passages, setPassages] = useState([]);
  const [badgesActifs, setBadgesActifs] = useState(0);
  const [badgesDisponibles, setBadgesDisponibles] = useState(0);
  const [effectifs, setEffectifs] = useState({
    militairesInternes: 0,
    militairesExternes: 0,
    civils: 0
  });

  const fetchData = () => {
    api.get('/personnels').then(res => {
      setPersonnels(res.data);
      // Calculer les effectifs par type
      const effectifsCalcules = res.data.reduce((acc, personnel) => {
        if (personnel.type === 'militaire_interne') acc.militairesInternes++;
        else if (personnel.type === 'militaire_externe') acc.militairesExternes++;
        else if (personnel.type === 'civil_externe') acc.civils++;
        return acc;
      }, { militairesInternes: 0, militairesExternes: 0, civils: 0 });
      setEffectifs(effectifsCalcules);
    });

    api.get('/passages').then(res => setPassages(res.data));

    api.get('/badges?statut=actif').then(res => setBadgesActifs(res.data.length));
    api.get('/badges?statut=disponible').then(res => setBadgesDisponibles(res.data.length));
  };

  useEffect(() => {
    fetchData();

    // Auto-refresh toutes les 5 secondes
    const interval = setInterval(fetchData, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Tableau de Bord</h1>
        <div className="text-sm text-gray-500">
          Mise à jour automatique toutes les 5 secondes
        </div>
      </div>

      <StatCards
        passages={passages.length}
        badgesActifs={badgesActifs}
        badgesDisponibles={badgesDisponibles}
        personnels={personnels.length}
        effectifs={effectifs}
      />

      <QuickActions onSuccess={fetchData} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <RealTimeHistory personnels={personnels} />
        <AccessLogTable passages={passages.slice(0, 10)} />
      </div>
    </div>
  );
}

export default Dashboard;