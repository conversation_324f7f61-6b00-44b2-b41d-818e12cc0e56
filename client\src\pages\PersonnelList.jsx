import { useEffect, useState } from 'react';
import api from '../services/api';

function PersonnelList() {
  const [personnels, setPersonnels] = useState([]);

  useEffect(() => {
    api.get('/personnels')
      .then((res) => setPersonnels(res.data))
      .catch((err) => console.error('❌ Erreur API :', err));
  }, []);

  return (
    <div className="p-4">
      <h1 className="text-xl font-bold mb-4">Liste du personnel</h1>
      <ul className="space-y-2">
        {personnels.map((p) => (
          <li key={p.id_personnel} className="bg-white p-2 shadow rounded">
            {p.nom} {p.prenom}
          </li>
        ))}
      </ul>
    </div>
  );
}

export default PersonnelList;
