import { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Calendar, Download, Filter } from 'lucide-react';
import {
  <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  LineChart, Line, PieChart, Pie, Cell, Area, AreaChart
} from 'recharts';
import { statisticsService } from '../services/apiService';
import { Card, CardHeader, CardContent, CardTitle, Alert } from '../components/ui';

function Statistics() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dateRange, setDateRange] = useState({
    debut: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 jours
    fin: new Date().toISOString().split('T')[0]
  });
  const [stats, setStats] = useState({
    passagesParJour: [],
    passagesParHeure: [],
    passagesParPorte: [],
    passagesParType: [],
    tendances: {},
    resume: {}
  });

  const fetchStatistics = async () => {
    try {
      setLoading(true);
      const data = await statisticsService.getFullStats(dateRange);
      setStats(data);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des statistiques');
      console.error('Error fetching statistics:', err);
      // Données par défaut en cas d'erreur
      setStats({
        passagesParJour: [],
        passagesParHeure: [],
        passagesParPorte: [],
        passagesParType: [],
        tendances: {},
        resume: {}
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, [dateRange]);

  const handleDateRangeChange = (field, value) => {
    setDateRange(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleExportReport = async () => {
    try {
      // Pour l'instant, on génère un rapport simple en JSON
      const reportData = {
        periode: `${dateRange.debut} - ${dateRange.fin}`,
        resume: stats.resume,
        donnees: {
          passagesParJour: stats.passagesParJour,
          passagesParHeure: stats.passagesParHeure,
          passagesParPorte: stats.passagesParPorte,
          passagesParType: stats.passagesParType
        },
        dateGeneration: new Date().toISOString()
      };

      const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(reportData, null, 2));
      const link = document.createElement('a');
      link.setAttribute('href', dataStr);
      link.setAttribute('download', `rapport_statistiques_${dateRange.debut}_${dateRange.fin}.json`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      setError('Erreur lors de l\'export du rapport');
    }
  };

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Chargement des statistiques...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Statistiques & Rapports</h1>
        <button
          onClick={handleExportReport}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <Download className="h-4 w-4" />
          <span>Exporter Rapport</span>
        </button>
      </div>

      {error && (
        <Alert variant="error" className="mb-6" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Filtres temporels */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Période d'analyse</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date début
              </label>
              <input
                type="date"
                value={dateRange.debut}
                onChange={(e) => handleDateRangeChange('debut', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date fin
              </label>
              <input
                type="date"
                value={dateRange.fin}
                onChange={(e) => handleDateRangeChange('fin', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
            <div className="flex items-end">
              <div className="grid grid-cols-2 gap-2 w-full">
                <button
                  onClick={() => setDateRange({
                    debut: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    fin: new Date().toISOString().split('T')[0]
                  })}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  7 jours
                </button>
                <button
                  onClick={() => setDateRange({
                    debut: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    fin: new Date().toISOString().split('T')[0]
                  })}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  30 jours
                </button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Résumé des métriques */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Passages</p>
                <p className="text-2xl font-bold text-gray-800">{stats.resume.totalPassages || 0}</p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +{stats.tendances.passagesEvolution || 0}% vs période précédente
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Taux d'Autorisation</p>
                <p className="text-2xl font-bold text-green-600">{stats.resume.tauxAutorisation || 0}%</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.resume.passagesAutorises || 0} autorisés / {stats.resume.totalPassages || 0} total
                </p>
              </div>
              <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                <span className="text-green-600 font-bold text-sm">✓</span>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pic d'Affluence</p>
                <p className="text-2xl font-bold text-orange-600">{stats.resume.heurePointe || '--:--'}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.resume.passagesHeurePointe || 0} passages
                </p>
              </div>
              <Calendar className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Badges Actifs</p>
                <p className="text-2xl font-bold text-purple-600">{stats.resume.badgesActifs || 0}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.resume.badgesVisiteurs || 0} visiteurs
                </p>
              </div>
              <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                <span className="text-purple-600 font-bold text-sm">ID</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Passages par jour */}
        <Card>
          <CardHeader>
            <CardTitle>Évolution des Passages par Jour</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={stats.passagesParJour}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="passages" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.3} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Passages par heure */}
        <Card>
          <CardHeader>
            <CardTitle>Répartition par Heure de la Journée</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={stats.passagesParHeure}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="heure" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="passages" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Passages par porte */}
        <Card>
          <CardHeader>
            <CardTitle>Répartition par Porte d'Accès</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={stats.passagesParPorte}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="passages"
                >
                  {stats.passagesParPorte.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Passages par type de personnel */}
        <Card>
          <CardHeader>
            <CardTitle>Répartition par Type de Personnel</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={stats.passagesParType} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="type" type="category" width={120} />
                <Tooltip />
                <Bar dataKey="passages" fill="#F59E0B" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default Statistics;
