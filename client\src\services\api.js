import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour les requêtes
api.interceptors.request.use(
  (config) => {
    // Ajouter le token d'authentification si disponible
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour les réponses
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Gestion globale des erreurs
    if (error.response) {
      // Erreur de réponse du serveur
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // Non autorisé - rediriger vers la connexion
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
          break;
        case 403:
          // Accès interdit
          console.error('Accès interdit:', data.message);
          break;
        case 404:
          // Ressource non trouvée
          console.error('Ressource non trouvée:', error.config.url);
          break;
        case 500:
          // Erreur serveur
          console.error('Erreur serveur:', data.message);
          break;
        default:
          console.error('Erreur API:', data.message || error.message);
      }
    } else if (error.request) {
      // Erreur de réseau
      console.error('Erreur de réseau - Serveur inaccessible');
    } else {
      // Autre erreur
      console.error('Erreur:', error.message);
    }

    return Promise.reject(error);
  }
);

export default api;