import api from './api';
import {
  adaptPersonnelFromBackend,
  adaptPersonnelToBackend,
  adaptBadgeFromBackend,
  adaptBadgeToBackend,
  adaptPassageFromBackend,
  adaptAttributionFromBackend,
  adaptReferenceFromBackend,
  adaptReferenceToBackend,
  adaptStatisticsFromBackend
} from './dataAdapter';

// Service API pour le personnel
export const personnelService = {
  // Lister le personnel
  async getAll(filters = {}) {
    const response = await api.get('/personnel', { params: filters });
    // Le backend retourne { personnel: [...], pagination: {...} }
    const personnelData = response.data.personnel || response.data;
    return Array.isArray(personnelData) ? personnelData.map(adaptPersonnelFromBackend) : [];
  },

  // Obtenir un personnel par ID
  async getById(id) {
    const response = await api.get(`/personnel/${id}`);
    return adaptPersonnelFromBackend(response.data);
  },

  // Créer un militaire interne
  async createMilitaireInterne(data) {
    const adaptedData = adaptPersonnelToBackend(data);
    const response = await api.post('/personnel/internes', adaptedData);
    return adaptPersonnelFromBackend(response.data);
  },

  // Créer un militaire externe
  async createMilitaireExterne(data) {
    const adaptedData = adaptPersonnelToBackend(data);
    const response = await api.post('/personnel/militaires-externes', adaptedData);
    return adaptPersonnelFromBackend(response.data);
  },

  // Créer un civil externe
  async createCivilExterne(data) {
    const adaptedData = adaptPersonnelToBackend(data);
    const response = await api.post('/personnel/civils-externes', adaptedData);
    return adaptPersonnelFromBackend(response.data);
  },

  // Créer un personnel (méthode générique)
  async create(data) {
    switch (data.type) {
      case 'militaire_interne':
        return this.createMilitaireInterne(data);
      case 'militaire_externe':
        return this.createMilitaireExterne(data);
      case 'civil_externe':
        return this.createCivilExterne(data);
      default:
        throw new Error('Type de personnel non supporté');
    }
  },

  // Mettre à jour un personnel
  async update(id, data) {
    const adaptedData = adaptPersonnelToBackend(data);
    const response = await api.put(`/personnel/${id}`, adaptedData);
    return adaptPersonnelFromBackend(response.data);
  },

  // Supprimer un personnel
  async delete(id) {
    await api.delete(`/personnel/${id}`);
  }
};

// Service API pour les badges
export const badgeService = {
  // Lister les badges
  async getAll(filters = {}) {
    const response = await api.get('/badges', { params: filters });
    // Le backend retourne { badges: [...], pagination: {...} }
    const badgesData = response.data.badges || response.data;
    return Array.isArray(badgesData) ? badgesData.map(adaptBadgeFromBackend) : [];
  },

  // Obtenir un badge par ID
  async getById(id) {
    const response = await api.get(`/badges/${id}`);
    return adaptBadgeFromBackend(response.data);
  },

  // Créer un badge visiteur
  async create(data) {
    const adaptedData = adaptBadgeToBackend(data);
    const response = await api.post('/badges/visiteurs', adaptedData);
    return adaptBadgeFromBackend(response.data);
  },

  // Mettre à jour un badge
  async update(id, data) {
    const adaptedData = adaptBadgeToBackend(data);
    const response = await api.put(`/badges/${id}`, adaptedData);
    return adaptBadgeFromBackend(response.data);
  },

  // Activer/Désactiver un badge
  async toggleStatus(id) {
    const response = await api.put(`/badges/${id}/toggle-statut`);
    return adaptBadgeFromBackend(response.data);
  },

  // Supprimer un badge
  async delete(id) {
    await api.delete(`/badges/${id}`);
  },

  // Obtenir les badges disponibles par type
  async getAvailableByType(type) {
    const response = await api.get(`/badges/disponibles/${type}`);
    return response.data.map(adaptBadgeFromBackend);
  },

  // Lister les attributions
  async getAttributions(filters = {}) {
    const response = await api.get('/badges/attributions/list', { params: filters });
    return response.data.map(adaptAttributionFromBackend);
  },

  // Clôturer une attribution
  async closeAttribution(id) {
    const response = await api.put(`/badges/attributions/${id}/cloturer`);
    return response.data;
  }
};

// Service API pour les passages
export const passageService = {
  // Lister les passages
  async getAll(filters = {}) {
    const response = await api.get('/passages', { params: filters });
    return response.data.map(adaptPassageFromBackend);
  },

  // Obtenir les passages récents
  async getRecent(limit = 10, minutes = 60) {
    const response = await api.get('/passages/recent', { 
      params: { limit, minutes } 
    });
    return response.data.map(adaptPassageFromBackend);
  },

  // Obtenir les statistiques des passages
  async getStatistics(periode = 24) {
    const response = await api.get('/passages/statistiques', { 
      params: { periode } 
    });
    return response.data;
  },

  // Enregistrer un passage
  async create(data) {
    const response = await api.post('/passages', data);
    return adaptPassageFromBackend(response.data);
  },

  // Vérifier l'accès d'un badge
  async verifyAccess(epcCode) {
    const response = await api.get(`/passages/verifier/${epcCode}`);
    return response.data;
  }
};

// Service API pour les données de référence
export const referenceService = {
  // Obtenir toutes les références
  async getAll() {
    const response = await api.get('/reference/all');
    return {
      grades: response.data.grades?.map(item => adaptReferenceFromBackend(item, 'grade')) || [],
      unites: response.data.unites?.map(item => adaptReferenceFromBackend(item, 'unite')) || [],
      typesPersonnel: response.data.types_personnel || [],
      typesBadge: response.data.types_badge?.map(item => adaptReferenceFromBackend(item, 'type_badge')) || [],
      portes: response.data.portes?.map(item => adaptReferenceFromBackend(item, 'porte')) || []
    };
  },

  // Obtenir les grades
  async getGrades() {
    const response = await api.get('/reference/grades');
    return response.data.map(item => adaptReferenceFromBackend(item, 'grade'));
  },

  // Obtenir les unités
  async getUnites() {
    const response = await api.get('/reference/unites');
    return response.data.map(item => adaptReferenceFromBackend(item, 'unite'));
  },

  // Obtenir les types de personnel
  async getTypesPersonnel() {
    const response = await api.get('/reference/types-personnel');
    return response.data;
  },

  // Obtenir les types de badge
  async getTypesBadge() {
    const response = await api.get('/reference/types-badge');
    return response.data.map(item => adaptReferenceFromBackend(item, 'type_badge'));
  },

  // Obtenir les portes
  async getPortes() {
    const response = await api.get('/reference/portes');
    return response.data.map(item => adaptReferenceFromBackend(item, 'porte'));
  },

  // Créer une référence
  async create(type, data) {
    const adaptedData = adaptReferenceToBackend(data, type);
    const response = await api.post(`/reference/${type}s`, adaptedData);
    return adaptReferenceFromBackend(response.data, type);
  },

  // Mettre à jour une référence
  async update(type, id, data) {
    const adaptedData = adaptReferenceToBackend(data, type);
    const response = await api.put(`/reference/${type}s/${id}`, adaptedData);
    return adaptReferenceFromBackend(response.data, type);
  },

  // Supprimer une référence
  async delete(type, id) {
    await api.delete(`/reference/${type}s/${id}`);
  }
};

// Service API pour les statistiques
export const statisticsService = {
  // Obtenir les statistiques des badges
  async getBadgeStats() {
    const response = await api.get('/statistiques/badges');
    return response.data;
  },

  // Obtenir l'historique des attributions
  async getAttributionHistory(limit = 20) {
    const response = await api.get('/statistiques/historique-attributions', {
      params: { limit }
    });
    return response.data.map(adaptAttributionFromBackend);
  },

  // Obtenir les badges expirant bientôt
  async getExpiringBadges(heures = 24) {
    const response = await api.get('/statistiques/badges-expirants', {
      params: { heures }
    });
    return response.data.map(adaptBadgeFromBackend);
  },

  // Obtenir le rapport d'utilisation
  async getUsageReport(periode = 7) {
    const response = await api.get('/statistiques/rapport-utilisation', {
      params: { periode }
    });
    return response.data;
  },

  // Obtenir les statistiques complètes (pour la page statistiques)
  async getFullStats(filters = {}) {
    // Cette méthode combine plusieurs appels pour obtenir toutes les stats
    const [badgeStats, passageStats, usageReport] = await Promise.all([
      this.getBadgeStats(),
      passageService.getStatistics(),
      this.getUsageReport()
    ]);

    return adaptStatisticsFromBackend({
      ...badgeStats,
      ...passageStats,
      ...usageReport
    });
  }
};
