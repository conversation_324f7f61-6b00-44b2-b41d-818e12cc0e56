// Service d'adaptation des données entre frontend et backend

/**
 * Adapte les données de personnel du backend vers le frontend
 */
export const adaptPersonnelFromBackend = (personnelData) => {
  if (!personnelData) return null;

  return {
    id: personnelData.id,
    nom: personnelData.nom,
    prenom: personnelData.prenom,
    type: personnelData.typePersonnel?.nom_type || personnelData.TypePersonnel?.nom_type || personnelData.type_personnel,
    matricule: personnelData.matricule,
    cin: personnelData.cin,
    grade: personnelData.grade?.nom_grade || personnelData.Grade?.nom_grade,
    unite: personnelData.unite?.nom_unite || personnelData.Unite?.nom_unite,
    fonction: personnelData.civilInfo?.fonction || personnelData.fonction,
    destination: personnelData.militaireExterneInfo?.destination || personnelData.civilInfo?.destination || personnelData.destination,
    unite_origine: personnelData.militaireExterneInfo?.unite_origine || personnelData.civilInfo?.unite_origine || personnelData.unite_origine,
    heure_entree: personnelData.militaireExterneInfo?.heure_entree || personnelData.civilInfo?.heure_entree || personnelData.heure_entree,
    badge_id: personnelData.badge?.id,
    badge_numero: personnelData.badge?.epc_code,
    date_creation: personnelData.created_at,
    date_modification: personnelData.updated_at
  };
};

/**
 * Adapte les données de personnel du frontend vers le backend
 */
export const adaptPersonnelToBackend = (personnelData) => {
  const baseData = {
    nom: personnelData.nom,
    prenom: personnelData.prenom,
    type_personnel: personnelData.type
  };

  // Ajouter les champs spécifiques selon le type
  if (personnelData.type === 'militaire_interne') {
    return {
      ...baseData,
      matricule: personnelData.matricule,
      grade: personnelData.grade,
      unite: personnelData.unite
    };
  } else if (personnelData.type === 'militaire_externe') {
    return {
      ...baseData,
      cin: personnelData.cin,
      grade: personnelData.grade,
      destination: personnelData.destination,
      unite_origine: personnelData.unite_origine,
      heure_entree: personnelData.heure_entree,
      badge_id: personnelData.badge_id
    };
  } else if (personnelData.type === 'civil_externe') {
    return {
      ...baseData,
      cin: personnelData.cin,
      fonction: personnelData.fonction,
      destination: personnelData.destination,
      unite_origine: personnelData.unite_origine,
      heure_entree: personnelData.heure_entree,
      badge_id: personnelData.badge_id
    };
  }

  return baseData;
};

/**
 * Adapte les données de badge du backend vers le frontend
 */
export const adaptBadgeFromBackend = (badgeData) => {
  if (!badgeData) return null;

  return {
    id: badgeData.id,
    numero: badgeData.epc_code, // Le numéro est stocké dans epc_code
    epc_code: badgeData.epc_code,
    type: badgeData.TypeBadge?.nom_type_badge || badgeData.type,
    statut: badgeData.actif ? (badgeData.permanent ? 'actif' : 'disponible') : 'desactive',
    date_creation: badgeData.created_at,
    date_expiration: badgeData.date_expiration,
    description: badgeData.description,
    personnel: badgeData.Personnel ? adaptPersonnelFromBackend(badgeData.Personnel) : null
  };
};

/**
 * Adapte les données de badge du frontend vers le backend
 */
export const adaptBadgeToBackend = (badgeData) => {
  return {
    numero: badgeData.numero,
    type_badge: badgeData.type,
    statut: badgeData.statut,
    description: badgeData.description
  };
};

/**
 * Adapte les données de passage du backend vers le frontend
 */
export const adaptPassageFromBackend = (passageData) => {
  if (!passageData) return null;
  
  return {
    id: passageData.id,
    date_passage: passageData.date_passage,
    type_acces: passageData.type_acces,
    resultat: passageData.resultat,
    motif_refus: passageData.motif_refus,
    badge_numero: passageData.badge?.numero,
    porte_nom: passageData.porte?.nom,
    personnel: passageData.personnel ? adaptPersonnelFromBackend(passageData.personnel) : null
  };
};

/**
 * Adapte les données d'attribution du backend vers le frontend
 */
export const adaptAttributionFromBackend = (attributionData) => {
  if (!attributionData) return null;
  
  return {
    id: attributionData.id,
    badge_id: attributionData.badge_id,
    badge_numero: attributionData.badge?.numero,
    badge_type: attributionData.badge?.type_badge?.nom,
    personnel_id: attributionData.personnel_id,
    personnel_nom: attributionData.personnel?.nom,
    personnel_prenom: attributionData.personnel?.prenom,
    personnel_type: attributionData.personnel?.type_personnel,
    personnel_matricule: attributionData.personnel?.matricule,
    personnel_cin: attributionData.personnel?.cin,
    date_attribution: attributionData.date_attribution,
    date_expiration: attributionData.date_expiration,
    statut: attributionData.statut,
    destination: attributionData.personnel?.destination,
    unite: attributionData.personnel?.unite?.nom
  };
};

/**
 * Adapte les données de référence du backend vers le frontend
 */
export const adaptReferenceFromBackend = (referenceData, type) => {
  if (!referenceData) return null;

  const baseData = {
    id: referenceData.id,
    description: referenceData.description,
    actif: referenceData.actif !== false // Par défaut true si pas défini
  };

  switch (type) {
    case 'grade':
      return {
        ...baseData,
        nom: referenceData.nom_grade,
        niveau: referenceData.niveau
      };
    case 'unite':
      return {
        ...baseData,
        nom: referenceData.nom_unite,
        code: referenceData.code_unite
      };
    case 'porte':
      return {
        ...baseData,
        nom: referenceData.libelle,
        localisation: referenceData.localisation,
        type_acces: referenceData.type_acces
      };
    case 'type_badge':
      return {
        ...baseData,
        nom: referenceData.nom_type_badge,
        couleur: referenceData.couleur,
        duree_validite: referenceData.duree_validite_heures
      };
    default:
      return {
        ...baseData,
        nom: referenceData.nom || referenceData.libelle || referenceData.nom_type
      };
  }
};

/**
 * Adapte les données de référence du frontend vers le backend
 */
export const adaptReferenceToBackend = (referenceData, type) => {
  const baseData = {
    nom: referenceData.nom,
    description: referenceData.description
  };

  switch (type) {
    case 'grade':
      return {
        ...baseData,
        niveau: referenceData.niveau
      };
    case 'unite':
      return {
        ...baseData,
        code: referenceData.code
      };
    case 'porte':
      return {
        ...baseData,
        localisation: referenceData.localisation,
        type_acces: referenceData.type_acces
      };
    case 'type_badge':
      return {
        ...baseData,
        couleur: referenceData.couleur,
        duree_validite_heures: referenceData.duree_validite
      };
    default:
      return baseData;
  }
};

/**
 * Adapte les statistiques du backend vers le frontend
 */
export const adaptStatisticsFromBackend = (statsData) => {
  if (!statsData) return null;
  
  return {
    passagesParJour: statsData.passages_par_jour || [],
    passagesParHeure: statsData.passages_par_heure || [],
    passagesParPorte: statsData.passages_par_porte || [],
    passagesParType: statsData.passages_par_type || [],
    tendances: statsData.tendances || {},
    resume: {
      totalPassages: statsData.total_passages || 0,
      passagesAutorises: statsData.passages_autorises || 0,
      tauxAutorisation: statsData.taux_autorisation || 0,
      heurePointe: statsData.heure_pointe || '--:--',
      passagesHeurePointe: statsData.passages_heure_pointe || 0,
      badgesActifs: statsData.badges_actifs || 0,
      badgesVisiteurs: statsData.badges_visiteurs || 0
    }
  };
};
