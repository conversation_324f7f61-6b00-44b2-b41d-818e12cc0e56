-- Script d'initialisation des badges visiteurs pour les tests
-- À exécuter après la création du schéma principal

-- Insertion de badges visiteurs militaires
INSERT INTO badge (epc_code, id_type_badge, permanent, actif) VALUES 
('EPC_VM_001', (SELECT id FROM type_badge WHERE nom_type_badge = 'badge_visiteur_militaire'), false, true),
('EPC_VM_002', (SELECT id FROM type_badge WHERE nom_type_badge = 'badge_visiteur_militaire'), false, true),
('EPC_VM_003', (SELECT id FROM type_badge WHERE nom_type_badge = 'badge_visiteur_militaire'), false, true),
('EPC_VM_004', (SELECT id FROM type_badge WHERE nom_type_badge = 'badge_visiteur_militaire'), false, true),
('EPC_VM_005', (SELECT id FROM type_badge WHERE nom_type_badge = 'badge_visiteur_militaire'), false, true);

-- Insertion de badges visiteurs civils
INSERT INTO badge (epc_code, id_type_badge, permanent, actif) VALUES 
('EPC_VC_001', (SELECT id FROM type_badge WHERE nom_type_badge = 'badge_visiteur_civil'), false, true),
('EPC_VC_002', (SELECT id FROM type_badge WHERE nom_type_badge = 'badge_visiteur_civil'), false, true),
('EPC_VC_003', (SELECT id FROM type_badge WHERE nom_type_badge = 'badge_visiteur_civil'), false, true),
('EPC_VC_004', (SELECT id FROM type_badge WHERE nom_type_badge = 'badge_visiteur_civil'), false, true),
('EPC_VC_005', (SELECT id FROM type_badge WHERE nom_type_badge = 'badge_visiteur_civil'), false, true);

-- Vérification des badges créés
SELECT 
    b.id,
    b.epc_code,
    tb.nom_type_badge,
    b.permanent,
    b.actif,
    b.created_at
FROM badge b
JOIN type_badge tb ON b.id_type_badge = tb.id
WHERE b.permanent = false
ORDER BY tb.nom_type_badge, b.epc_code;
