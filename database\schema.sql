-- Schéma de base de données pour le système de contrôle d'accès militaire
-- PostgreSQL

-- Suppression des tables existantes (ordre inverse des dépendances)
DROP TABLE IF EXISTS passage CASCADE;
DROP TABLE IF EXISTS attribution_badge CASCADE;
DROP TABLE IF EXISTS civil_info CASCADE;
DROP TABLE IF EXISTS militaire_externe_info CASCADE;
DROP TABLE IF EXISTS personnel CASCADE;
DROP TABLE IF EXISTS badge CASCADE;
DROP TABLE IF EXISTS porte CASCADE;
DROP TABLE IF EXISTS unite CASCADE;
DROP TABLE IF EXISTS grade CASCADE;
DROP TABLE IF EXISTS type_badge CASCADE;
DROP TABLE IF EXISTS type_personnel CASCADE;

-- Table des types de personnel
CREATE TABLE type_personnel (
    id SERIAL PRIMARY KEY,
    nom_type VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des grades militaires
CREATE TABLE grade (
    id SERIAL PRIMARY KEY,
    nom_grade VARCHAR(50) NOT NULL UNIQUE,
    niveau INTEGER NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des unités
CREATE TABLE unite (
    id SERIAL PRIMARY KEY,
    nom_unite VARCHAR(100) NOT NULL,
    code_unite VARCHAR(20) UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des types de badge
CREATE TABLE type_badge (
    id SERIAL PRIMARY KEY,
    nom_type_badge VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des portes
CREATE TABLE porte (
    id SERIAL PRIMARY KEY,
    libelle VARCHAR(100) NOT NULL,
    description TEXT,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des badges
CREATE TABLE badge (
    id SERIAL PRIMARY KEY,
    epc_code VARCHAR(100) NOT NULL UNIQUE,
    id_type_badge INTEGER NOT NULL REFERENCES type_badge(id),
    permanent BOOLEAN DEFAULT FALSE,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table principale du personnel
CREATE TABLE personnel (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    matricule VARCHAR(50),
    cin VARCHAR(20),
    id_type_personnel INTEGER NOT NULL REFERENCES type_personnel(id),
    id_grade INTEGER REFERENCES grade(id),
    id_unite INTEGER REFERENCES unite(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Contraintes
    CONSTRAINT unique_matricule UNIQUE (matricule),
    CONSTRAINT unique_cin UNIQUE (cin)
);

-- Table des informations spécifiques aux militaires externes
CREATE TABLE militaire_externe_info (
    id_personnel INTEGER PRIMARY KEY REFERENCES personnel(id) ON DELETE CASCADE,
    horaire_entree TIMESTAMP,
    objet_visite TEXT,
    destination INTEGER REFERENCES unite(id),
    id_unite_origine INTEGER REFERENCES unite(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des informations spécifiques aux civils
CREATE TABLE civil_info (
    id_personnel INTEGER PRIMARY KEY REFERENCES personnel(id) ON DELETE CASCADE,
    horaire_entree TIMESTAMP,
    objet_visite TEXT,
    destination INTEGER REFERENCES unite(id),
    societe VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des attributions de badges
CREATE TABLE attribution_badge (
    id SERIAL PRIMARY KEY,
    id_personnel INTEGER NOT NULL REFERENCES personnel(id),
    id_badge INTEGER NOT NULL REFERENCES badge(id),
    date_attribution TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_fin TIMESTAMP,
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'expire', 'desactive')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Un badge ne peut être attribué qu'à une seule personne à la fois
    CONSTRAINT unique_badge_actif UNIQUE (id_badge, statut) DEFERRABLE INITIALLY DEFERRED
);

-- Table des passages
CREATE TABLE passage (
    id SERIAL PRIMARY KEY,
    id_badge INTEGER NOT NULL REFERENCES badge(id),
    id_porte INTEGER NOT NULL REFERENCES porte(id),
    date_acces TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    type_acces VARCHAR(10) NOT NULL CHECK (type_acces IN ('entree', 'sortie')),
    resultat VARCHAR(20) DEFAULT 'autorise' CHECK (resultat IN ('autorise', 'refuse', 'erreur')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour optimiser les performances
CREATE INDEX idx_personnel_type ON personnel(id_type_personnel);
CREATE INDEX idx_personnel_matricule ON personnel(matricule);
CREATE INDEX idx_badge_epc ON badge(epc_code);
CREATE INDEX idx_attribution_statut ON attribution_badge(statut);
CREATE INDEX idx_attribution_personnel ON attribution_badge(id_personnel);
CREATE INDEX idx_passage_date ON passage(date_acces);
CREATE INDEX idx_passage_badge ON passage(id_badge);

-- Données de référence
INSERT INTO type_personnel (nom_type, description) VALUES 
('militaire_interne', 'Personnel militaire permanent du site'),
('militaire_externe', 'Personnel militaire en visite'),
('civil_externe', 'Personnel civil en visite');

INSERT INTO type_badge (nom_type_badge, description) VALUES 
('badge_militaire_interne', 'Badge permanent pour militaires internes'),
('badge_visiteur_militaire', 'Badge temporaire pour militaires externes'),
('badge_visiteur_civil', 'Badge temporaire pour civils visiteurs');

INSERT INTO grade (nom_grade, niveau, description) VALUES 
('Soldat', 1, 'Grade de base'),
('Caporal', 2, 'Sous-officier junior'),
('Sergent', 3, 'Sous-officier'),
('Adjudant', 4, 'Sous-officier supérieur'),
('Lieutenant', 5, 'Officier subalterne'),
('Capitaine', 6, 'Officier'),
('Commandant', 7, 'Officier supérieur'),
('Colonel', 8, 'Officier supérieur'),
('Général', 9, 'Officier général');

INSERT INTO unite (nom_unite, code_unite, description) VALUES 
('État-Major', 'EM', 'État-Major du site'),
('Sécurité', 'SEC', 'Service de sécurité'),
('Logistique', 'LOG', 'Service logistique'),
('Communication', 'COM', 'Service communication'),
('Maintenance', 'MAINT', 'Service maintenance');

INSERT INTO porte (libelle, description) VALUES 
('Entrée Principale', 'Porte d''entrée principale du site'),
('Entrée Secondaire', 'Porte d''entrée secondaire'),
('Sortie Véhicules', 'Sortie dédiée aux véhicules');

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
CREATE TRIGGER update_type_personnel_updated_at BEFORE UPDATE ON type_personnel FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_grade_updated_at BEFORE UPDATE ON grade FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_unite_updated_at BEFORE UPDATE ON unite FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_type_badge_updated_at BEFORE UPDATE ON type_badge FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_porte_updated_at BEFORE UPDATE ON porte FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_badge_updated_at BEFORE UPDATE ON badge FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_personnel_updated_at BEFORE UPDATE ON personnel FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_militaire_externe_info_updated_at BEFORE UPDATE ON militaire_externe_info FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_civil_info_updated_at BEFORE UPDATE ON civil_info FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_attribution_badge_updated_at BEFORE UPDATE ON attribution_badge FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
