{"name": "controle-acces-militaire", "version": "1.0.0", "description": "Système de contrôle d'accès pour site militaire avec gestion RFID", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm start", "server:start": "cd server && npm start", "client:build": "cd client && npm run build", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "test": "cd server && npm test"}, "keywords": ["controle-acces", "rfid", "militaire", "express", "react", "postgresql"], "author": "Système de Contrôle d'Accès", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"axios": "^1.10.0"}}