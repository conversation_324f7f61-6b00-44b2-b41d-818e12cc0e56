const { 
  Badge, 
  TypeBadge, 
  AttributionBadge, 
  Personnel,
  TypePersonnel,
  Grade,
  Unite
} = require('../models');
const { 
  getBadgesDisponibles, 
  cloturerBadgeVisiteur,
  generateEPCCode 
} = require('../utils/badgeUtils');
const { sequelize } = require('../config/database');

/**
 * Récupérer tous les badges avec leurs attributions
 */
const listerBadges = async (req, res) => {
  try {
    const { type, statut, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;
    
    let whereClause = {};
    let includeClause = [
      { model: TypeBadge, as: 'typeBadge' },
      {
        model: AttributionBadge,
        as: 'attributions',
        include: [
          {
            model: Personnel,
            as: 'personnel',
            include: [
              { model: TypePersonnel, as: 'typePersonnel' },
              { model: Grade, as: 'grade' },
              { model: Unite, as: 'unite' }
            ]
          }
        ],
        required: false
      }
    ];
    
    // Filtre par type de badge
    if (type) {
      includeClause[0].where = { nom_type_badge: type };
    }
    
    // Filtre par statut
    if (statut) {
      whereClause.actif = statut === 'actif';
    }
    
    const { count, rows } = await Badge.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });
    
    res.json({
      badges: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    });
    
  } catch (error) {
    console.error('Erreur lors de la récupération des badges:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des badges',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Récupérer les badges visiteurs disponibles par type
 */
const obtenirBadgesDisponibles = async (req, res) => {
  try {
    const { type } = req.params;
    
    // Valider le type de badge
    const typesValides = ['badge_visiteur_militaire', 'badge_visiteur_civil'];
    if (!typesValides.includes(type)) {
      return res.status(400).json({
        message: 'Type de badge invalide',
        typesValides
      });
    }
    
    const badges = await getBadgesDisponibles(type);
    
    res.json({
      badges,
      count: badges.length,
      type
    });
    
  } catch (error) {
    console.error('Erreur lors de la récupération des badges disponibles:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des badges disponibles',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Créer un nouveau badge visiteur
 */
const creerBadgeVisiteur = async (req, res) => {
  try {
    const { type_badge } = req.validatedData;
    
    // Vérifier que le type de badge existe
    const typeBadge = await TypeBadge.findOne({
      where: { nom_type_badge: type_badge }
    });
    
    if (!typeBadge) {
      return res.status(400).json({
        message: 'Type de badge non trouvé'
      });
    }
    
    // Vérifier que c'est bien un badge visiteur
    if (!type_badge.includes('visiteur')) {
      return res.status(400).json({
        message: 'Seuls les badges visiteurs peuvent être créés via cette route'
      });
    }
    
    // Créer le badge
    const badge = await Badge.create({
      epc_code: generateEPCCode(),
      id_type_badge: typeBadge.id,
      permanent: false,
      actif: true
    });
    
    // Récupérer le badge complet
    const badgeComplet = await Badge.findByPk(badge.id, {
      include: [{ model: TypeBadge, as: 'typeBadge' }]
    });
    
    res.status(201).json({
      message: 'Badge visiteur créé avec succès',
      badge: badgeComplet
    });
    
  } catch (error) {
    console.error('Erreur lors de la création du badge visiteur:', error);
    res.status(500).json({
      message: 'Erreur lors de la création du badge visiteur',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Récupérer un badge par ID avec son historique
 */
const obtenirBadge = async (req, res) => {
  try {
    const { id } = req.params;
    
    const badge = await Badge.findByPk(id, {
      include: [
        { model: TypeBadge, as: 'typeBadge' },
        {
          model: AttributionBadge,
          as: 'attributions',
          include: [
            {
              model: Personnel,
              as: 'personnel',
              include: [
                { model: TypePersonnel, as: 'typePersonnel' },
                { model: Grade, as: 'grade' },
                { model: Unite, as: 'unite' }
              ]
            }
          ],
          order: [['date_attribution', 'DESC']]
        }
      ]
    });
    
    if (!badge) {
      return res.status(404).json({
        message: 'Badge non trouvé'
      });
    }
    
    res.json({ badge });
    
  } catch (error) {
    console.error('Erreur lors de la récupération du badge:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération du badge',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Lister toutes les attributions avec filtres
 */
const listerAttributions = async (req, res) => {
  try {
    const { statut, type_personnel, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = {};
    let includeClause = [
      {
        model: Personnel,
        as: 'personnel',
        include: [
          { model: TypePersonnel, as: 'typePersonnel' },
          { model: Grade, as: 'grade' },
          { model: Unite, as: 'unite' }
        ]
      },
      {
        model: Badge,
        as: 'badge',
        include: [{ model: TypeBadge, as: 'typeBadge' }]
      }
    ];

    // Filtre par statut d'attribution
    if (statut) {
      whereClause.statut = statut;
    }

    // Filtre par type de personnel
    if (type_personnel) {
      includeClause[0].include[0].where = { nom_type: type_personnel };
    }

    const { count, rows } = await AttributionBadge.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['date_attribution', 'DESC']]
    });

    res.json({
      attributions: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des attributions:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des attributions',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Clôturer une attribution de badge visiteur
 */
const cloturerAttribution = async (req, res) => {
  try {
    const { id } = req.params;

    const attribution = await cloturerBadgeVisiteur(id);

    // Récupérer l'attribution complète mise à jour
    const attributionComplete = await AttributionBadge.findByPk(attribution.id, {
      include: [
        {
          model: Personnel,
          as: 'personnel',
          include: [
            { model: TypePersonnel, as: 'typePersonnel' },
            { model: Grade, as: 'grade' },
            { model: Unite, as: 'unite' }
          ]
        },
        {
          model: Badge,
          as: 'badge',
          include: [{ model: TypeBadge, as: 'typeBadge' }]
        }
      ]
    });

    res.json({
      message: 'Attribution clôturée avec succès',
      attribution: attributionComplete
    });

  } catch (error) {
    console.error('Erreur lors de la clôture de l\'attribution:', error);
    res.status(500).json({
      message: error.message || 'Erreur lors de la clôture de l\'attribution',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Désactiver/Réactiver un badge
 */
const toggleBadgeStatut = async (req, res) => {
  try {
    const { id } = req.params;

    const badge = await Badge.findByPk(id);

    if (!badge) {
      return res.status(404).json({
        message: 'Badge non trouvé'
      });
    }

    // Vérifier s'il y a des attributions actives
    if (!badge.actif) {
      // Réactivation : vérifier qu'il n'y a pas d'attribution active
      const attributionActive = await AttributionBadge.findOne({
        where: {
          id_badge: id,
          statut: 'actif'
        }
      });

      if (attributionActive) {
        return res.status(400).json({
          message: 'Impossible de réactiver un badge avec une attribution active'
        });
      }
    }

    // Basculer le statut
    await badge.update({
      actif: !badge.actif
    });

    // Récupérer le badge mis à jour
    const badgeComplet = await Badge.findByPk(id, {
      include: [{ model: TypeBadge, as: 'typeBadge' }]
    });

    res.json({
      message: `Badge ${badge.actif ? 'activé' : 'désactivé'} avec succès`,
      badge: badgeComplet
    });

  } catch (error) {
    console.error('Erreur lors du changement de statut du badge:', error);
    res.status(500).json({
      message: 'Erreur lors du changement de statut du badge',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

module.exports = {
  listerBadges,
  obtenirBadgesDisponibles,
  creerBadgeVisiteur,
  obtenirBadge,
  listerAttributions,
  cloturerAttribution,
  toggleBadgeStatut
};
