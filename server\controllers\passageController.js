const { 
  Passage, 
  Badge, 
  TypeBadge, 
  AttributionBadge, 
  Personnel,
  TypePersonnel,
  Grade,
  Unite,
  Porte
} = require('../models');
const { Op } = require('sequelize');

/**
 * Enregistrer un nouveau passage RFID
 */
const enregistrerPassage = async (req, res) => {
  try {
    const { epc_code, id_porte, type_acces } = req.validatedData;
    
    // Vérifier que le badge existe et est actif
    const badge = await Badge.findOne({
      where: { 
        epc_code: epc_code,
        actif: true 
      },
      include: [
        { model: TypeBadge, as: 'typeBadge' },
        {
          model: AttributionBadge,
          as: 'attributions',
          where: { statut: 'actif' },
          include: [
            {
              model: Personnel,
              as: 'personnel',
              include: [
                { model: TypePersonnel, as: 'typePersonnel' },
                { model: Grade, as: 'grade' },
                { model: Unite, as: 'unite' }
              ]
            }
          ],
          required: false
        }
      ]
    });
    
    if (!badge) {
      // Badge non trouvé ou inactif - on enregistre quand même le passage pour audit
      return res.status(403).json({
        message: 'Accès refusé - Badge non reconnu ou inactif',
        epc_code,
        resultat: 'refuse',
        date_acces: new Date()
      });
    }
    
    // Vérifier qu'il y a une attribution active
    const attributionActive = badge.attributions && badge.attributions.length > 0 
      ? badge.attributions[0] 
      : null;
    
    if (!attributionActive) {
      // Badge trouvé mais pas d'attribution active
      const passage = await Passage.create({
        id_badge: badge.id,
        id_porte,
        type_acces,
        resultat: 'refuse',
        date_acces: new Date()
      });
      
      return res.status(403).json({
        message: 'Accès refusé - Badge non attribué',
        passage: {
          id: passage.id,
          epc_code: badge.epc_code,
          resultat: 'refuse',
          date_acces: passage.date_acces
        }
      });
    }
    
    // Accès autorisé
    const passage = await Passage.create({
      id_badge: badge.id,
      id_porte,
      type_acces,
      resultat: 'autorise',
      date_acces: new Date()
    });
    
    // Récupérer le passage complet avec toutes les relations
    const passageComplet = await Passage.findByPk(passage.id, {
      include: [
        {
          model: Badge,
          as: 'badge',
          include: [
            { model: TypeBadge, as: 'typeBadge' },
            {
              model: AttributionBadge,
              as: 'attributions',
              where: { statut: 'actif' },
              include: [
                {
                  model: Personnel,
                  as: 'personnel',
                  include: [
                    { model: TypePersonnel, as: 'typePersonnel' },
                    { model: Grade, as: 'grade' },
                    { model: Unite, as: 'unite' }
                  ]
                }
              ],
              required: false
            }
          ]
        },
        { model: Porte, as: 'porte' }
      ]
    });
    
    res.status(201).json({
      message: 'Passage enregistré avec succès',
      passage: passageComplet
    });
    
  } catch (error) {
    console.error('Erreur lors de l\'enregistrement du passage:', error);
    res.status(500).json({
      message: 'Erreur lors de l\'enregistrement du passage',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Récupérer l'historique des passages avec filtres
 */
const obtenirHistoriquePassages = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      type_acces, 
      resultat, 
      id_porte,
      date_debut,
      date_fin,
      epc_code,
      type_personnel
    } = req.query;
    
    const offset = (page - 1) * limit;
    
    let whereClause = {};
    let includeClause = [
      {
        model: Badge,
        as: 'badge',
        include: [
          { model: TypeBadge, as: 'typeBadge' },
          {
            model: AttributionBadge,
            as: 'attributions',
            include: [
              {
                model: Personnel,
                as: 'personnel',
                include: [
                  { model: TypePersonnel, as: 'typePersonnel' },
                  { model: Grade, as: 'grade' },
                  { model: Unite, as: 'unite' }
                ]
              }
            ],
            required: false
          }
        ],
        required: false
      },
      { model: Porte, as: 'porte' }
    ];
    
    // Filtres
    if (type_acces) {
      whereClause.type_acces = type_acces;
    }
    
    if (resultat) {
      whereClause.resultat = resultat;
    }
    
    if (id_porte) {
      whereClause.id_porte = parseInt(id_porte);
    }
    
    if (date_debut || date_fin) {
      whereClause.date_acces = {};
      if (date_debut) {
        whereClause.date_acces[Op.gte] = new Date(date_debut);
      }
      if (date_fin) {
        whereClause.date_acces[Op.lte] = new Date(date_fin);
      }
    }
    
    if (epc_code) {
      includeClause[0].where = { epc_code: { [Op.iLike]: `%${epc_code}%` } };
      includeClause[0].required = true;
    }
    
    if (type_personnel) {
      includeClause[0].include[1].include[0].include[0].where = { nom_type: type_personnel };
      includeClause[0].include[1].required = true;
      includeClause[0].required = true;
    }
    
    const { count, rows } = await Passage.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['date_acces', 'DESC']]
    });
    
    res.json({
      passages: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    });
    
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération de l\'historique',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Récupérer les passages récents (temps réel)
 */
const obtenirPassagesRecents = async (req, res) => {
  try {
    const { limit = 10, minutes = 60 } = req.query;

    const dateLimit = new Date();
    dateLimit.setMinutes(dateLimit.getMinutes() - parseInt(minutes));

    const passages = await Passage.findAll({
      where: {
        date_acces: {
          [Op.gte]: dateLimit
        }
      },
      include: [
        {
          model: Badge,
          as: 'badge',
          include: [
            { model: TypeBadge, as: 'typeBadge' },
            {
              model: AttributionBadge,
              as: 'attributions',
              where: { statut: 'actif' },
              include: [
                {
                  model: Personnel,
                  as: 'personnel',
                  include: [
                    { model: TypePersonnel, as: 'typePersonnel' },
                    { model: Grade, as: 'grade' },
                    { model: Unite, as: 'unite' }
                  ]
                }
              ],
              required: false
            }
          ],
          required: false
        },
        { model: Porte, as: 'porte' }
      ],
      limit: parseInt(limit),
      order: [['date_acces', 'DESC']]
    });

    res.json({
      passages,
      count: passages.length,
      periode_minutes: parseInt(minutes)
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des passages récents:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des passages récents',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Obtenir les statistiques des passages
 */
const obtenirStatistiquesPassages = async (req, res) => {
  try {
    const { periode = 24 } = req.query; // Période en heures
    const dateDebut = new Date();
    dateDebut.setHours(dateDebut.getHours() - parseInt(periode));

    // Statistiques générales
    const totalPassages = await Passage.count({
      where: {
        date_acces: { [Op.gte]: dateDebut }
      }
    });

    const passagesAutorises = await Passage.count({
      where: {
        date_acces: { [Op.gte]: dateDebut },
        resultat: 'autorise'
      }
    });

    const passagesRefuses = await Passage.count({
      where: {
        date_acces: { [Op.gte]: dateDebut },
        resultat: 'refuse'
      }
    });

    const entrees = await Passage.count({
      where: {
        date_acces: { [Op.gte]: dateDebut },
        type_acces: 'entree',
        resultat: 'autorise'
      }
    });

    const sorties = await Passage.count({
      where: {
        date_acces: { [Op.gte]: dateDebut },
        type_acces: 'sortie',
        resultat: 'autorise'
      }
    });

    // Passages par porte
    const passagesParPorte = await Passage.findAll({
      where: {
        date_acces: { [Op.gte]: dateDebut }
      },
      include: [{ model: Porte, as: 'porte' }],
      attributes: [
        'id_porte',
        [require('sequelize').fn('COUNT', require('sequelize').col('Passage.id')), 'total']
      ],
      group: ['Passage.id_porte', 'porte.id'],
      raw: false
    });

    res.json({
      periode_heures: parseInt(periode),
      date_debut: dateDebut,
      statistiques: {
        total: totalPassages,
        autorises: passagesAutorises,
        refuses: passagesRefuses,
        entrees: entrees,
        sorties: sorties,
        taux_autorisation: totalPassages > 0 ? Math.round((passagesAutorises / totalPassages) * 100) : 0
      },
      par_porte: passagesParPorte
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des statistiques',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Vérifier l'accès d'un badge (simulation lecteur RFID)
 */
const verifierAccesBadge = async (req, res) => {
  try {
    const { epc_code } = req.params;

    const badge = await Badge.findOne({
      where: {
        epc_code: epc_code,
        actif: true
      },
      include: [
        { model: TypeBadge, as: 'typeBadge' },
        {
          model: AttributionBadge,
          as: 'attributions',
          where: { statut: 'actif' },
          include: [
            {
              model: Personnel,
              as: 'personnel',
              include: [
                { model: TypePersonnel, as: 'typePersonnel' },
                { model: Grade, as: 'grade' },
                { model: Unite, as: 'unite' }
              ]
            }
          ],
          required: false
        }
      ]
    });

    if (!badge) {
      return res.status(404).json({
        acces_autorise: false,
        message: 'Badge non reconnu ou inactif',
        epc_code
      });
    }

    const attributionActive = badge.attributions && badge.attributions.length > 0
      ? badge.attributions[0]
      : null;

    if (!attributionActive) {
      return res.status(403).json({
        acces_autorise: false,
        message: 'Badge non attribué',
        badge: {
          epc_code: badge.epc_code,
          type: badge.typeBadge.nom_type_badge
        }
      });
    }

    res.json({
      acces_autorise: true,
      message: 'Accès autorisé',
      badge: {
        epc_code: badge.epc_code,
        type: badge.typeBadge.nom_type_badge,
        permanent: badge.permanent
      },
      personnel: attributionActive.personnel
    });

  } catch (error) {
    console.error('Erreur lors de la vérification d\'accès:', error);
    res.status(500).json({
      message: 'Erreur lors de la vérification d\'accès',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

module.exports = {
  enregistrerPassage,
  obtenirHistoriquePassages,
  obtenirPassagesRecents,
  obtenirStatistiquesPassages,
  verifierAccesBadge
};
