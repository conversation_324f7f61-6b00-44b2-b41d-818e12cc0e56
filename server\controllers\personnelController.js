const { 
  Personnel, 
  TypePersonnel, 
  Grade, 
  Unite, 
  MilitaireExterneInfo, 
  CivilInfo,
  AttributionBadge,
  Badge
} = require('../models');
const { creerBadgePermanent, attribuerBadgeVisiteur } = require('../utils/badgeUtils');
const { sequelize } = require('../config/database');

/**
 * Créer un militaire interne avec badge permanent automatique
 */
const creerMilitaireInterne = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { nom, prenom, matricule, id_grade, id_unite } = req.validatedData;
    
    // Vérifier que le matricule n'existe pas déjà
    const existant = await Personnel.findOne({ where: { matricule } });
    if (existant) {
      await transaction.rollback();
      return res.status(400).json({
        message: 'Un personnel avec ce matricule existe déjà'
      });
    }
    
    // Récupérer le type personnel militaire interne
    const typePersonnel = await TypePersonnel.findOne({
      where: { nom_type: 'militaire_interne' }
    });
    
    if (!typePersonnel) {
      await transaction.rollback();
      return res.status(500).json({
        message: 'Type de personnel militaire interne non trouvé'
      });
    }
    
    // Créer le personnel
    const personnel = await Personnel.create({
      nom,
      prenom,
      matricule,
      id_type_personnel: typePersonnel.id,
      id_grade,
      id_unite
    }, { transaction });
    
    // Créer automatiquement un badge permanent
    const { badge, attribution } = await creerBadgePermanent(personnel.id, transaction);
    
    await transaction.commit();
    
    // Récupérer le personnel complet avec relations
    const personnelComplet = await Personnel.findByPk(personnel.id, {
      include: [
        { model: TypePersonnel, as: 'typePersonnel' },
        { model: Grade, as: 'grade' },
        { model: Unite, as: 'unite' },
        {
          model: AttributionBadge,
          as: 'attributions',
          include: [{ model: Badge, as: 'badge' }],
          where: { statut: 'actif' },
          required: false
        }
      ]
    });
    
    res.status(201).json({
      message: 'Militaire interne créé avec succès',
      personnel: personnelComplet,
      badge: {
        epc_code: badge.epc_code,
        attribution_id: attribution.id
      }
    });
    
  } catch (error) {
    await transaction.rollback();
    console.error('Erreur lors de la création du militaire interne:', error);
    res.status(500).json({
      message: 'Erreur lors de la création du militaire interne',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Créer un militaire externe avec attribution manuelle de badge
 */
const creerMilitaireExterne = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      nom, prenom, matricule, cin, id_grade,
      horaire_entree, id_unite_origine, destination, objet_visite,
      id_badge
    } = req.validatedData;
    
    // Vérifier que le matricule n'existe pas déjà
    const existant = await Personnel.findOne({ where: { matricule } });
    if (existant) {
      await transaction.rollback();
      return res.status(400).json({
        message: 'Un personnel avec ce matricule existe déjà'
      });
    }
    
    // Récupérer le type personnel militaire externe
    const typePersonnel = await TypePersonnel.findOne({
      where: { nom_type: 'militaire_externe' }
    });
    
    if (!typePersonnel) {
      await transaction.rollback();
      return res.status(500).json({
        message: 'Type de personnel militaire externe non trouvé'
      });
    }
    
    // Créer le personnel
    const personnel = await Personnel.create({
      nom,
      prenom,
      matricule,
      cin,
      id_type_personnel: typePersonnel.id,
      id_grade
    }, { transaction });
    
    // Créer les informations spécifiques
    await MilitaireExterneInfo.create({
      id_personnel: personnel.id,
      horaire_entree: new Date(horaire_entree),
      objet_visite,
      destination,
      id_unite_origine
    }, { transaction });
    
    // Attribuer le badge visiteur
    const attribution = await attribuerBadgeVisiteur(personnel.id, id_badge, transaction);

    await transaction.commit();
    
    // Récupérer le personnel complet
    const personnelComplet = await Personnel.findByPk(personnel.id, {
      include: [
        { model: TypePersonnel, as: 'typePersonnel' },
        { model: Grade, as: 'grade' },
        { 
          model: MilitaireExterneInfo, 
          as: 'militaireExterneInfo',
          include: [
            { model: Unite, as: 'uniteDestination' },
            { model: Unite, as: 'uniteOrigine' }
          ]
        },
        {
          model: AttributionBadge,
          as: 'attributions',
          include: [{ model: Badge, as: 'badge' }],
          where: { statut: 'actif' },
          required: false
        }
      ]
    });
    
    res.status(201).json({
      message: 'Militaire externe créé avec succès',
      personnel: personnelComplet,
      attribution_id: attribution.id
    });
    
  } catch (error) {
    await transaction.rollback();
    console.error('Erreur lors de la création du militaire externe:', error);
    res.status(500).json({
      message: 'Erreur lors de la création du militaire externe',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Créer un civil externe avec attribution manuelle de badge
 */
const creerCivilExterne = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      nom, prenom, cin, societe,
      horaire_entree, destination, objet_visite,
      id_badge
    } = req.validatedData;

    // Vérifier que le CIN n'existe pas déjà
    const existant = await Personnel.findOne({ where: { cin } });
    if (existant) {
      await transaction.rollback();
      return res.status(400).json({
        message: 'Un personnel avec ce CIN existe déjà'
      });
    }

    // Récupérer le type personnel civil externe
    const typePersonnel = await TypePersonnel.findOne({
      where: { nom_type: 'civil_externe' }
    });

    if (!typePersonnel) {
      await transaction.rollback();
      return res.status(500).json({
        message: 'Type de personnel civil externe non trouvé'
      });
    }

    // Créer le personnel
    const personnel = await Personnel.create({
      nom,
      prenom,
      cin,
      id_type_personnel: typePersonnel.id
    }, { transaction });

    // Créer les informations spécifiques
    await CivilInfo.create({
      id_personnel: personnel.id,
      horaire_entree: new Date(horaire_entree),
      objet_visite,
      destination,
      societe
    }, { transaction });

    // Attribuer le badge visiteur
    const attribution = await attribuerBadgeVisiteur(personnel.id, id_badge, transaction);

    await transaction.commit();

    // Récupérer le personnel complet
    const personnelComplet = await Personnel.findByPk(personnel.id, {
      include: [
        { model: TypePersonnel, as: 'typePersonnel' },
        {
          model: CivilInfo,
          as: 'civilInfo',
          include: [{ model: Unite, as: 'uniteDestination' }]
        },
        {
          model: AttributionBadge,
          as: 'attributions',
          include: [{ model: Badge, as: 'badge' }],
          where: { statut: 'actif' },
          required: false
        }
      ]
    });

    res.status(201).json({
      message: 'Civil externe créé avec succès',
      personnel: personnelComplet,
      attribution_id: attribution.id
    });

  } catch (error) {
    await transaction.rollback();
    console.error('Erreur lors de la création du civil externe:', error);
    res.status(500).json({
      message: 'Erreur lors de la création du civil externe',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Lister tout le personnel avec filtres
 */
const listerPersonnel = async (req, res) => {
  try {
    const { type, page = 1, limit = 10, search } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = {};
    let includeClause = [
      { model: TypePersonnel, as: 'typePersonnel' },
      { model: Grade, as: 'grade' },
      { model: Unite, as: 'unite' },
      { model: MilitaireExterneInfo, as: 'militaireExterneInfo', required: false },
      { model: CivilInfo, as: 'civilInfo', required: false },
      {
        model: AttributionBadge,
        as: 'attributions',
        include: [{ model: Badge, as: 'badge' }],
        where: { statut: 'actif' },
        required: false
      }
    ];

    // Filtre par type de personnel
    if (type) {
      includeClause[0].where = { nom_type: type };
    }

    // Recherche textuelle
    if (search) {
      const { Op } = require('sequelize');
      whereClause = {
        [Op.or]: [
          { nom: { [Op.iLike]: `%${search}%` } },
          { prenom: { [Op.iLike]: `%${search}%` } },
          { matricule: { [Op.iLike]: `%${search}%` } },
          { cin: { [Op.iLike]: `%${search}%` } }
        ]
      };
    }

    const { count, rows } = await Personnel.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    res.json({
      personnel: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération du personnel:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération du personnel',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Récupérer un personnel par ID
 */
const obtenirPersonnel = async (req, res) => {
  try {
    const { id } = req.params;

    const personnel = await Personnel.findByPk(id, {
      include: [
        { model: TypePersonnel, as: 'typePersonnel' },
        { model: Grade, as: 'grade' },
        { model: Unite, as: 'unite' },
        {
          model: MilitaireExterneInfo,
          as: 'militaireExterneInfo',
          include: [
            { model: Unite, as: 'uniteDestination' },
            { model: Unite, as: 'uniteOrigine' }
          ],
          required: false
        },
        {
          model: CivilInfo,
          as: 'civilInfo',
          include: [{ model: Unite, as: 'uniteDestination' }],
          required: false
        },
        {
          model: AttributionBadge,
          as: 'attributions',
          include: [{ model: Badge, as: 'badge' }]
        }
      ]
    });

    if (!personnel) {
      return res.status(404).json({
        message: 'Personnel non trouvé'
      });
    }

    res.json({ personnel });

  } catch (error) {
    console.error('Erreur lors de la récupération du personnel:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération du personnel',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

module.exports = {
  creerMilitaireInterne,
  creerMilitaireExterne,
  creerCivilExterne,
  listerPersonnel,
  obtenirPersonnel
};
