const { Grade, Unite, TypePersonnel, TypeBadge, Porte } = require('../models');

/**
 * Récupérer tous les grades
 */
const obtenirGrades = async (req, res) => {
  try {
    const grades = await Grade.findAll({
      order: [['niveau', 'ASC']]
    });
    
    res.json({ grades });
  } catch (error) {
    console.error('Erreur lors de la récupération des grades:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des grades',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Récupérer toutes les unités
 */
const obtenirUnites = async (req, res) => {
  try {
    const unites = await Unite.findAll({
      order: [['nom_unite', 'ASC']]
    });
    
    res.json({ unites });
  } catch (error) {
    console.error('Erreur lors de la récupération des unités:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des unités',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Récupérer tous les types de personnel
 */
const obtenirTypesPersonnel = async (req, res) => {
  try {
    const types = await TypePersonnel.findAll({
      order: [['nom_type', 'ASC']]
    });
    
    res.json({ types });
  } catch (error) {
    console.error('Erreur lors de la récupération des types de personnel:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des types de personnel',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Récupérer tous les types de badge
 */
const obtenirTypesBadge = async (req, res) => {
  try {
    const types = await TypeBadge.findAll({
      order: [['nom_type_badge', 'ASC']]
    });
    
    res.json({ types });
  } catch (error) {
    console.error('Erreur lors de la récupération des types de badge:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des types de badge',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Récupérer toutes les portes
 */
const obtenirPortes = async (req, res) => {
  try {
    const portes = await Porte.findAll({
      where: { actif: true },
      order: [['libelle', 'ASC']]
    });
    
    res.json({ portes });
  } catch (error) {
    console.error('Erreur lors de la récupération des portes:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des portes',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Récupérer toutes les données de référence en une seule requête
 */
const obtenirToutesReferences = async (req, res) => {
  try {
    const [grades, unites, typesPersonnel, typesBadge, portes] = await Promise.all([
      Grade.findAll({ order: [['niveau', 'ASC']] }),
      Unite.findAll({ order: [['nom_unite', 'ASC']] }),
      TypePersonnel.findAll({ order: [['nom_type', 'ASC']] }),
      TypeBadge.findAll({ order: [['nom_type_badge', 'ASC']] }),
      Porte.findAll({ where: { actif: true }, order: [['libelle', 'ASC']] })
    ]);
    
    res.json({
      grades,
      unites,
      typesPersonnel,
      typesBadge,
      portes
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des références:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des références',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

module.exports = {
  obtenirGrades,
  obtenirUnites,
  obtenirTypesPersonnel,
  obtenirTypesBadge,
  obtenirPortes,
  obtenirToutesReferences
};
