const { 
  Badge, 
  TypeBadge, 
  AttributionBadge, 
  Personnel,
  TypePersonnel,
  Passage
} = require('../models');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Obtenir les statistiques générales des badges
 */
const obtenirStatistiquesBadges = async (req, res) => {
  try {
    // Statistiques simples
    const totalBadges = await Badge.count();
    const badgesActifs = await Badge.count({ where: { actif: true } });
    const badgesPermanents = await Badge.count({ where: { permanent: true } });

    // Statistiques par type de badge
    const badgesMilitairesInternes = await Badge.count({
      include: [{
        model: TypeBadge,
        as: 'typeBadge',
        where: { nom_type_badge: 'badge_militaire_interne' }
      }]
    });

    const badgesVisiteursMilitaires = await Badge.count({
      include: [{
        model: TypeBadge,
        as: 'typeBadge',
        where: { nom_type_badge: 'badge_visiteur_militaire' }
      }]
    });

    const badgesVisiteursCivils = await Badge.count({
      include: [{
        model: TypeBadge,
        as: 'typeBadge',
        where: { nom_type_badge: 'badge_visiteur_civil' }
      }]
    });

    // Statistiques des attributions actives
    const attributionsActives = await AttributionBadge.count({
      where: { statut: 'actif' }
    });

    // Badges visiteurs disponibles
    const badgesVisiteursDisponibles = await Badge.count({
      include: [{
        model: TypeBadge,
        as: 'typeBadge',
        where: {
          nom_type_badge: {
            [Op.in]: ['badge_visiteur_militaire', 'badge_visiteur_civil']
          }
        }
      }],
      where: {
        permanent: false,
        actif: true,
        id: {
          [Op.notIn]: sequelize.literal(`(
            SELECT id_badge FROM attribution_badge WHERE statut = 'actif'
          )`)
        }
      }
    });

    // Personnel avec badges actifs par type
    const personnelMilitaireInterne = await Personnel.count({
      include: [
        {
          model: TypePersonnel,
          as: 'typePersonnel',
          where: { nom_type: 'militaire_interne' }
        },
        {
          model: AttributionBadge,
          as: 'attributions',
          where: { statut: 'actif' },
          required: true
        }
      ]
    });

    const personnelMilitaireExterne = await Personnel.count({
      include: [
        {
          model: TypePersonnel,
          as: 'typePersonnel',
          where: { nom_type: 'militaire_externe' }
        },
        {
          model: AttributionBadge,
          as: 'attributions',
          where: { statut: 'actif' },
          required: true
        }
      ]
    });

    const personnelCivilExterne = await Personnel.count({
      include: [
        {
          model: TypePersonnel,
          as: 'typePersonnel',
          where: { nom_type: 'civil_externe' }
        },
        {
          model: AttributionBadge,
          as: 'attributions',
          where: { statut: 'actif' },
          required: true
        }
      ]
    });

    res.json({
      badges: {
        total: totalBadges,
        actifs: badgesActifs,
        permanents: badgesPermanents,
        parType: {
          militaire_interne: badgesMilitairesInternes,
          visiteur_militaire: badgesVisiteursMilitaires,
          visiteur_civil: badgesVisiteursCivils
        },
        attributionsActives,
        visiteursDisponibles: badgesVisiteursDisponibles
      },
      personnel: {
        avecBadgeActif: {
          militaire_interne: personnelMilitaireInterne,
          militaire_externe: personnelMilitaireExterne,
          civil_externe: personnelCivilExterne
        }
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des statistiques',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Obtenir l'historique des attributions récentes
 */
const obtenirHistoriqueAttributions = async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    const historique = await AttributionBadge.findAll({
      include: [
        {
          model: Personnel,
          as: 'personnel',
          include: [
            { model: TypePersonnel, as: 'typePersonnel' }
          ]
        },
        {
          model: Badge,
          as: 'badge',
          include: [{ model: TypeBadge, as: 'typeBadge' }]
        }
      ],
      order: [['date_attribution', 'DESC']],
      limit: parseInt(limit)
    });

    res.json({
      historique,
      count: historique.length
    });

  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération de l\'historique',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Obtenir les badges en fin de validité (pour les visiteurs)
 */
const obtenirBadgesExpirantBientot = async (req, res) => {
  try {
    const { heures = 24 } = req.query;
    const dateLimit = new Date();
    dateLimit.setHours(dateLimit.getHours() + parseInt(heures));

    const badgesExpirants = await AttributionBadge.findAll({
      where: {
        statut: 'actif',
        date_fin: {
          [Op.and]: [
            { [Op.not]: null },
            { [Op.lte]: dateLimit }
          ]
        }
      },
      include: [
        {
          model: Personnel,
          as: 'personnel',
          include: [
            { model: TypePersonnel, as: 'typePersonnel' }
          ]
        },
        {
          model: Badge,
          as: 'badge',
          include: [{ model: TypeBadge, as: 'typeBadge' }]
        }
      ],
      order: [['date_fin', 'ASC']]
    });

    res.json({
      badgesExpirants,
      count: badgesExpirants.length,
      heuresAvant: parseInt(heures)
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des badges expirants:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des badges expirants',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

/**
 * Obtenir un rapport d'utilisation des badges
 */
const obtenirRapportUtilisation = async (req, res) => {
  try {
    const { periode = 7 } = req.query; // Période en jours
    const dateDebut = new Date();
    dateDebut.setDate(dateDebut.getDate() - parseInt(periode));

    // Nouvelles attributions dans la période
    const nouvellesAttributions = await AttributionBadge.count({
      where: {
        date_attribution: {
          [Op.gte]: dateDebut
        }
      }
    });

    // Attributions clôturées dans la période
    const attributionsClôturées = await AttributionBadge.count({
      where: {
        date_fin: {
          [Op.gte]: dateDebut
        },
        statut: 'expire'
      }
    });

    // Badges les plus utilisés (version simplifiée pour éviter les erreurs de relation)
    const badgesPlusUtilises = await Badge.findAll({
      include: [
        {
          model: TypeBadge,
          as: 'TypeBadge',
          required: false
        }
      ],
      limit: 10,
      order: [['id', 'ASC']]
    });

    res.json({
      periode: parseInt(periode),
      dateDebut,
      nouvellesAttributions,
      attributionsClôturées,
      badgesPlusUtilises
    });

  } catch (error) {
    console.error('Erreur lors de la génération du rapport:', error);
    res.status(500).json({
      message: 'Erreur lors de la génération du rapport',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

module.exports = {
  obtenirStatistiquesBadges,
  obtenirHistoriqueAttributions,
  obtenirBadgesExpirantBientot,
  obtenirRapportUtilisation
};
