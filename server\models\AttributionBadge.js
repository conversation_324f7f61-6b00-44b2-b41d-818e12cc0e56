const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const AttributionBadge = sequelize.define('AttributionBadge', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  id_personnel: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'personnel',
      key: 'id'
    }
  },
  id_badge: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'badge',
      key: 'id'
    }
  },
  date_attribution: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  date_fin: {
    type: DataTypes.DATE,
    allowNull: true
  },
  statut: {
    type: DataTypes.STRING(20),
    defaultValue: 'actif',
    validate: {
      isIn: [['actif', 'expire', 'desactive']]
    }
  }
}, {
  tableName: 'attribution_badge',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = AttributionBadge;
