const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Badge = sequelize.define('Badge', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  epc_code: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true
  },
  id_type_badge: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'type_badge',
      key: 'id'
    }
  },
  permanent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  actif: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'badge',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = Badge;
