const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CivilInfo = sequelize.define('CivilInfo', {
  id_personnel: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    references: {
      model: 'personnel',
      key: 'id'
    }
  },
  horaire_entree: {
    type: DataTypes.DATE,
    allowNull: true
  },
  objet_visite: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  destination: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'unite',
      key: 'id'
    }
  },
  societe: {
    type: DataTypes.STRING(100),
    allowNull: true
  }
}, {
  tableName: 'civil_info',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = CivilInfo;
