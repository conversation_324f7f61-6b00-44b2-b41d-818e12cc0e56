const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const MilitaireExterneInfo = sequelize.define('MilitaireExterneInfo', {
  id_personnel: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    references: {
      model: 'personnel',
      key: 'id'
    }
  },
  horaire_entree: {
    type: DataTypes.DATE,
    allowNull: true
  },
  objet_visite: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  destination: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'unite',
      key: 'id'
    }
  },
  id_unite_origine: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'unite',
      key: 'id'
    }
  }
}, {
  tableName: 'militaire_externe_info',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = MilitaireExterneInfo;
