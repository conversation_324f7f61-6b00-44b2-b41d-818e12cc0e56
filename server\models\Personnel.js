const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Personnel = sequelize.define('Personnel', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  nom: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  prenom: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  matricule: {
    type: DataTypes.STRING(50),
    allowNull: true,
    unique: true
  },
  cin: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true
  },
  id_type_personnel: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'type_personnel',
      key: 'id'
    }
  },
  id_grade: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'grade',
      key: 'id'
    }
  },
  id_unite: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'unite',
      key: 'id'
    }
  }
}, {
  tableName: 'personnel',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = Personnel;
