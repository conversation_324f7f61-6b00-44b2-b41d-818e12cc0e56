const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const TypeBadge = sequelize.define('TypeBadge', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  nom_type_badge: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      isIn: [['badge_militaire_interne', 'badge_visiteur_militaire', 'badge_visiteur_civil']]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'type_badge',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = TypeBadge;
