const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const TypePersonnel = sequelize.define('TypePersonnel', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  nom_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      isIn: [['militaire_interne', 'militaire_externe', 'civil_externe']]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'type_personnel',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = TypePersonnel;
