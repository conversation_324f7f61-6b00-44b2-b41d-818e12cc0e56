const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Unite = sequelize.define('Unite', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  nom_unite: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  code_unite: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'unite',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = Unite;
