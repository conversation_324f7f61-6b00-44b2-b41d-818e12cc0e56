const { sequelize } = require('../config/database');

// Import des modèles
const TypePersonnel = require('./TypePersonnel');
const Grade = require('./Grade');
const Unite = require('./Unite');
const TypeBadge = require('./TypeBadge');
const Porte = require('./Porte');
const Badge = require('./Badge');
const Personnel = require('./Personnel');
const MilitaireExterneInfo = require('./MilitaireExterneInfo');
const CivilInfo = require('./CivilInfo');
const AttributionBadge = require('./AttributionBadge');
const Passage = require('./Passage');

// Définition des associations

// Personnel associations
Personnel.belongsTo(TypePersonnel, { 
  foreignKey: 'id_type_personnel', 
  as: 'typePersonnel' 
});
TypePersonnel.hasMany(Personnel, { 
  foreignKey: 'id_type_personnel', 
  as: 'personnel' 
});

Personnel.belongsTo(Grade, { 
  foreignKey: 'id_grade', 
  as: 'grade' 
});
Grade.hasMany(Personnel, { 
  foreignKey: 'id_grade', 
  as: 'personnel' 
});

Personnel.belongsTo(Unite, { 
  foreignKey: 'id_unite', 
  as: 'unite' 
});
Unite.hasMany(Personnel, { 
  foreignKey: 'id_unite', 
  as: 'personnel' 
});

// Informations spécifiques
Personnel.hasOne(MilitaireExterneInfo, { 
  foreignKey: 'id_personnel', 
  as: 'militaireExterneInfo' 
});
MilitaireExterneInfo.belongsTo(Personnel, { 
  foreignKey: 'id_personnel', 
  as: 'personnel' 
});

Personnel.hasOne(CivilInfo, { 
  foreignKey: 'id_personnel', 
  as: 'civilInfo' 
});
CivilInfo.belongsTo(Personnel, { 
  foreignKey: 'id_personnel', 
  as: 'personnel' 
});

// Associations pour les unités dans les infos spécifiques
MilitaireExterneInfo.belongsTo(Unite, { 
  foreignKey: 'destination', 
  as: 'uniteDestination' 
});
MilitaireExterneInfo.belongsTo(Unite, { 
  foreignKey: 'id_unite_origine', 
  as: 'uniteOrigine' 
});

CivilInfo.belongsTo(Unite, { 
  foreignKey: 'destination', 
  as: 'uniteDestination' 
});

// Badge associations
Badge.belongsTo(TypeBadge, { 
  foreignKey: 'id_type_badge', 
  as: 'typeBadge' 
});
TypeBadge.hasMany(Badge, { 
  foreignKey: 'id_type_badge', 
  as: 'badges' 
});

// Attribution Badge associations
AttributionBadge.belongsTo(Personnel, { 
  foreignKey: 'id_personnel', 
  as: 'personnel' 
});
Personnel.hasMany(AttributionBadge, { 
  foreignKey: 'id_personnel', 
  as: 'attributions' 
});

AttributionBadge.belongsTo(Badge, { 
  foreignKey: 'id_badge', 
  as: 'badge' 
});
Badge.hasMany(AttributionBadge, { 
  foreignKey: 'id_badge', 
  as: 'attributions' 
});

// Passage associations
Passage.belongsTo(Badge, { 
  foreignKey: 'id_badge', 
  as: 'badge' 
});
Badge.hasMany(Passage, { 
  foreignKey: 'id_badge', 
  as: 'passages' 
});

Passage.belongsTo(Porte, { 
  foreignKey: 'id_porte', 
  as: 'porte' 
});
Porte.hasMany(Passage, { 
  foreignKey: 'id_porte', 
  as: 'passages' 
});

// Export des modèles
module.exports = {
  sequelize,
  TypePersonnel,
  Grade,
  Unite,
  TypeBadge,
  Porte,
  Badge,
  Personnel,
  MilitaireExterneInfo,
  CivilInfo,
  AttributionBadge,
  Passage
};
