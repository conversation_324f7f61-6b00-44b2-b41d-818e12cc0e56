const express = require('express');
const router = express.Router();
const { validationMiddleware, schemas } = require('../utils/validation');
const {
  listerBadges,
  obtenirBadgesDisponibles,
  creerBadgeVisiteur,
  obtenirBadge,
  listerAttributions,
  cloturerAttribution,
  toggleBadgeStatut
} = require('../controllers/badgeController');

/**
 * @route GET /api/badges
 * @desc Lister tous les badges avec filtres et pagination
 * @access Public (à sécuriser plus tard)
 * @query type - Type de badge (badge_militaire_interne, badge_visiteur_militaire, badge_visiteur_civil)
 * @query statut - Statut du badge (actif, inactif)
 * @query page - Numéro de page (défaut: 1)
 * @query limit - Nombre d'éléments par page (défaut: 10)
 */
router.get('/', listerBadges);

/**
 * @route GET /api/badges/disponibles/:type
 * @desc Récupérer les badges visiteurs disponibles par type
 * @access Public (à sécuriser plus tard)
 * @param type - Type de badge (badge_visiteur_militaire ou badge_visiteur_civil)
 */
router.get('/disponibles/:type', obtenirBadgesDisponibles);

/**
 * @route POST /api/badges/visiteurs
 * @desc Créer un nouveau badge visiteur
 * @access Public (à sécuriser plus tard)
 * @body type_badge - Type de badge visiteur à créer
 */
router.post('/visiteurs', 
  validationMiddleware(schemas.badgeCreation),
  creerBadgeVisiteur
);

/**
 * @route GET /api/badges/:id
 * @desc Récupérer un badge par ID avec son historique
 * @access Public (à sécuriser plus tard)
 */
router.get('/:id', obtenirBadge);

/**
 * @route PUT /api/badges/:id/toggle-statut
 * @desc Activer/Désactiver un badge
 * @access Public (à sécuriser plus tard)
 */
router.put('/:id/toggle-statut', toggleBadgeStatut);

/**
 * @route GET /api/badges/attributions/list
 * @desc Lister toutes les attributions avec filtres
 * @access Public (à sécuriser plus tard)
 * @query statut - Statut de l'attribution (actif, expire, desactive)
 * @query type_personnel - Type de personnel (militaire_interne, militaire_externe, civil_externe)
 * @query page - Numéro de page (défaut: 1)
 * @query limit - Nombre d'éléments par page (défaut: 10)
 */
router.get('/attributions/list', listerAttributions);

/**
 * @route PUT /api/badges/attributions/:id/cloturer
 * @desc Clôturer une attribution de badge visiteur
 * @access Public (à sécuriser plus tard)
 */
router.put('/attributions/:id/cloturer', cloturerAttribution);

module.exports = router;
