const express = require('express');
const router = express.Router();
const { validationMiddleware, schemas } = require('../utils/validation');
const {
  enregistrerPassage,
  obtenirHistoriquePassages,
  obtenirPassagesRecents,
  obtenirStatistiquesPassages,
  verifierAccesBadge
} = require('../controllers/passageController');

/**
 * @route POST /api/passages
 * @desc Enregistrer un nouveau passage RFID
 * @access Public (lecteurs RFID)
 * @body epc_code - Code EPC du badge RFID
 * @body id_porte - ID de la porte
 * @body type_acces - Type d'accès (entree/sortie)
 */
router.post('/', 
  validationMiddleware(schemas.passage),
  enregistrerPassage
);

/**
 * @route GET /api/passages
 * @desc Récupérer l'historique des passages avec filtres
 * @access Public (à sécuriser plus tard)
 * @query page - Numéro de page (défaut: 1)
 * @query limit - Nombre d'éléments par page (défaut: 20)
 * @query type_acces - Type d'accès (entree/sortie)
 * @query resultat - Résultat (autorise/refuse/erreur)
 * @query id_porte - ID de la porte
 * @query date_debut - Date de début (ISO)
 * @query date_fin - Date de fin (ISO)
 * @query epc_code - Code EPC du badge (recherche partielle)
 * @query type_personnel - Type de personnel (militaire_interne/militaire_externe/civil_externe)
 */
router.get('/', obtenirHistoriquePassages);

/**
 * @route GET /api/passages/recent
 * @desc Récupérer les passages récents (temps réel)
 * @access Public (à sécuriser plus tard)
 * @query limit - Nombre d'éléments à retourner (défaut: 10)
 * @query minutes - Période en minutes (défaut: 60)
 */
router.get('/recent', obtenirPassagesRecents);

/**
 * @route GET /api/passages/statistiques
 * @desc Obtenir les statistiques des passages
 * @access Public (à sécuriser plus tard)
 * @query periode - Période en heures (défaut: 24)
 */
router.get('/statistiques', obtenirStatistiquesPassages);

/**
 * @route GET /api/passages/verifier/:epc_code
 * @desc Vérifier l'accès d'un badge (simulation lecteur RFID)
 * @access Public (lecteurs RFID)
 * @param epc_code - Code EPC du badge à vérifier
 */
router.get('/verifier/:epc_code', verifierAccesBadge);

module.exports = router;
