const express = require('express');
const router = express.Router();
const { validationMiddleware, schemas } = require('../utils/validation');
const {
  creerMilitaireInterne,
  creerMilitaireExterne,
  creerCivilExterne,
  listerPersonnel,
  obtenirPersonnel
} = require('../controllers/personnelController');

/**
 * @route POST /api/personnel/internes
 * @desc Créer un militaire interne avec badge permanent automatique
 * @access Public (à sécuriser plus tard)
 */
router.post('/internes', 
  validationMiddleware(schemas.militaireInterne),
  creerMilitaireInterne
);

/**
 * @route POST /api/personnel/militaires-externes
 * @desc Créer un militaire externe avec badge visiteur manuel
 * @access Public (à sécuriser plus tard)
 */
router.post('/militaires-externes', 
  validationMiddleware(schemas.militaireExterne),
  creerMilitaireExterne
);

/**
 * @route POST /api/personnel/civils-externes
 * @desc Créer un civil externe avec badge visiteur manuel
 * @access Public (à sécuriser plus tard)
 */
router.post('/civils-externes', 
  validationMiddleware(schemas.civil),
  creerCivilExterne
);

/**
 * @route GET /api/personnel
 * @desc Lister le personnel avec filtres et pagination
 * @access Public (à sécuriser plus tard)
 * @query type - Type de personnel (militaire_interne, militaire_externe, civil_externe)
 * @query page - Numéro de page (défaut: 1)
 * @query limit - Nombre d'éléments par page (défaut: 10)
 * @query search - Recherche textuelle dans nom, prénom, matricule, CIN
 */
router.get('/', listerPersonnel);

/**
 * @route GET /api/personnel/:id
 * @desc Récupérer un personnel par ID
 * @access Public (à sécuriser plus tard)
 */
router.get('/:id', obtenirPersonnel);

module.exports = router;
