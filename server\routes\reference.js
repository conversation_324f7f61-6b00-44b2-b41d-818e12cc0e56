const express = require('express');
const router = express.Router();
const {
  obtenirGrades,
  obtenirUnites,
  obtenirTypesPersonnel,
  obtenirTypesBadge,
  obtenirPortes,
  obtenirToutesReferences
} = require('../controllers/referenceController');

/**
 * @route GET /api/reference/grades
 * @desc Récupérer tous les grades militaires
 * @access Public
 */
router.get('/grades', obtenirGrades);

/**
 * @route GET /api/reference/unites
 * @desc Récupérer toutes les unités
 * @access Public
 */
router.get('/unites', obtenirUnites);

/**
 * @route GET /api/reference/types-personnel
 * @desc Récupérer tous les types de personnel
 * @access Public
 */
router.get('/types-personnel', obtenirTypesPersonnel);

/**
 * @route GET /api/reference/types-badge
 * @desc Récupérer tous les types de badge
 * @access Public
 */
router.get('/types-badge', obtenirTypesBadge);

/**
 * @route GET /api/reference/portes
 * @desc Récupérer toutes les portes actives
 * @access Public
 */
router.get('/portes', obtenirPortes);

/**
 * @route GET /api/reference/all
 * @desc Récupérer toutes les données de référence
 * @access Public
 */
router.get('/all', obtenirToutesReferences);

module.exports = router;
