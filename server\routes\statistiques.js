const express = require('express');
const router = express.Router();
const {
  obtenirStatistiquesBadges,
  obtenirHistoriqueAttributions,
  obtenirBadgesExpirantBientot,
  obtenirRapportUtilisation
} = require('../controllers/statistiquesController');

/**
 * @route GET /api/statistiques/badges
 * @desc Obtenir les statistiques générales des badges
 * @access Public (à sécuriser plus tard)
 */
router.get('/badges', obtenirStatistiquesBadges);

/**
 * @route GET /api/statistiques/historique-attributions
 * @desc Obtenir l'historique des attributions récentes
 * @access Public (à sécuriser plus tard)
 * @query limit - Nombre d'éléments à retourner (défaut: 20)
 */
router.get('/historique-attributions', obtenirHistoriqueAttributions);

/**
 * @route GET /api/statistiques/badges-expirants
 * @desc Obtenir les badges en fin de validité
 * @access Public (à sécuriser plus tard)
 * @query heures - Nombre d'heures avant expiration (défaut: 24)
 */
router.get('/badges-expirants', obtenirBadgesExpirantBientot);

/**
 * @route GET /api/statistiques/rapport-utilisation
 * @desc Obtenir un rapport d'utilisation des badges
 * @access Public (à sécuriser plus tard)
 * @query periode - Période en jours (défaut: 7)
 */
router.get('/rapport-utilisation', obtenirRapportUtilisation);

module.exports = router;
