/**
 * Script pour réinitialiser le rate limiting
 * Utile pendant le développement
 */

console.log('🔄 Réinitialisation du rate limiting...');

// En développement, le rate limiting est désactivé
// Ce script est juste informatif
console.log('ℹ️  Le rate limiting est désactivé en mode développement');
console.log('✅ Aucune action nécessaire');

// Si vous voulez forcer la réinitialisation en production,
// vous pouvez redémarrer le serveur ou implémenter un cache Redis
console.log('💡 Pour réinitialiser en production : redémarrez le serveur');

process.exit(0);
