const { sequelize } = require('../config/database');
const {
  TypePersonnel,
  Grade,
  Unite,
  TypeBadge,
  Porte,
  Badge,
  Personnel,
  MilitaireExterneInfo,
  CivilInfo,
  AttributionBadge,
  Passage
} = require('../models');

async function seedData() {
  try {
    console.log('🌱 Début de l\'insertion des données de test...');

    // 1. Types de personnel
    const typesPersonnel = await TypePersonnel.bulkCreate([
      { nom_type: 'militaire_interne', description: 'Personnel militaire interne' },
      { nom_type: 'militaire_externe', description: 'Personnel militaire externe (visiteur)' },
      { nom_type: 'civil_externe', description: 'Personnel civil externe (visiteur)' }
    ], { ignoreDuplicates: true });

    // 2. Grades
    const grades = await Grade.bulkCreate([
      { nom_grade: 'Général', niveau: 10, description: 'Grade le plus élevé' },
      { nom_grade: 'Colonel', niveau: 9, description: 'Officier supérieur' },
      { nom_grade: 'Lieutenant-Colonel', niveau: 8, description: 'Officier supérieur' },
      { nom_grade: 'Commandant', niveau: 7, description: 'Officier supérieur' },
      { nom_grade: 'Capitaine', niveau: 6, description: 'Officier' },
      { nom_grade: 'Lieutenant', niveau: 5, description: 'Officier' },
      { nom_grade: 'Sous-Lieutenant', niveau: 4, description: 'Officier' },
      { nom_grade: 'Adjudant-Chef', niveau: 3, description: 'Sous-officier' },
      { nom_grade: 'Adjudant', niveau: 2, description: 'Sous-officier' },
      { nom_grade: 'Sergent', niveau: 1, description: 'Sous-officier' }
    ], { ignoreDuplicates: true });

    // 3. Unités
    const unites = await Unite.bulkCreate([
      { nom_unite: '1er Régiment d\'Infanterie', code_unite: '1RI', description: 'Unité d\'infanterie' },
      { nom_unite: '2ème Régiment de Cavalerie', code_unite: '2RC', description: 'Unité de cavalerie' },
      { nom_unite: 'État-Major', code_unite: 'EM', description: 'État-major général' },
      { nom_unite: 'Service de Santé', code_unite: 'SS', description: 'Service médical' },
      { nom_unite: 'Génie Militaire', code_unite: 'GM', description: 'Corps du génie' },
      { nom_unite: 'Logistique', code_unite: 'LOG', description: 'Service logistique' }
    ], { ignoreDuplicates: true });

    // 4. Types de badges
    const typesBadge = await TypeBadge.bulkCreate([
      { nom_type_badge: 'militaire_interne', couleur: '#3B82F6', duree_validite_heures: null, description: 'Badge permanent pour militaires internes' },
      { nom_type_badge: 'visiteur', couleur: '#F59E0B', duree_validite_heures: 24, description: 'Badge temporaire pour visiteurs' }
    ], { ignoreDuplicates: true });

    // 5. Portes
    const portes = await Porte.bulkCreate([
      { libelle: 'Entrée Principale', localisation: 'Façade Nord', type_acces: 'entree', description: 'Entrée principale du site' },
      { libelle: 'Sortie Principale', localisation: 'Façade Nord', type_acces: 'sortie', description: 'Sortie principale du site' },
      { libelle: 'Entrée Service', localisation: 'Côté Est', type_acces: 'entree', description: 'Entrée de service' },
      { libelle: 'Sortie Urgence', localisation: 'Côté Ouest', type_acces: 'sortie', description: 'Sortie d\'urgence' }
    ], { ignoreDuplicates: true });

    // 6. Badges
    const badges = await Badge.bulkCreate([
      { epc_code: 'EPC001', id_type_badge: 1, permanent: true, actif: true },
      { epc_code: 'EPC002', id_type_badge: 1, permanent: true, actif: true },
      { epc_code: 'EPC003', id_type_badge: 1, permanent: true, actif: true },
      { epc_code: 'EPC101', id_type_badge: 2, permanent: false, actif: true },
      { epc_code: 'EPC102', id_type_badge: 2, permanent: false, actif: true },
      { epc_code: 'EPC103', id_type_badge: 2, permanent: false, actif: true },
      { epc_code: 'EPC104', id_type_badge: 2, permanent: false, actif: true },
      { epc_code: 'EPC105', id_type_badge: 2, permanent: false, actif: true }
    ], { ignoreDuplicates: true });

    // 7. Personnel militaire interne
    const personnelInterne = await Personnel.bulkCreate([
      {
        nom: 'Dupont',
        prenom: 'Jean',
        matricule: 'M001',
        id_type_personnel: 1, // militaire_interne
        id_grade: 5, // Capitaine
        id_unite: 1 // 1er RI
      },
      {
        nom: 'Martin',
        prenom: 'Pierre',
        matricule: 'M002',
        id_type_personnel: 1, // militaire_interne
        id_grade: 6, // Lieutenant
        id_unite: 2 // 2ème RC
      },
      {
        nom: 'Bernard',
        prenom: 'Marie',
        matricule: 'M003',
        id_type_personnel: 1, // militaire_interne
        id_grade: 4, // Commandant
        id_unite: 3 // EM
      }
    ], { ignoreDuplicates: true });

    console.log('✅ Données de base insérées avec succès !');

    console.log('✅ Données de test insérées avec succès !');
    console.log(`📊 Résumé :`);
    console.log(`   - ${typesPersonnel.length} types de personnel`);
    console.log(`   - ${grades.length} grades`);
    console.log(`   - ${unites.length} unités`);
    console.log(`   - ${typesBadge.length} types de badge`);
    console.log(`   - ${portes.length} portes`);
    console.log(`   - ${badges.length} badges`);
    console.log(`   - ${personnelInterne.length} personnel`);

  } catch (error) {
    console.error('❌ Erreur lors de l\'insertion des données:', error);
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  seedData().then(() => {
    console.log('🏁 Script terminé');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Erreur fatale:', error);
    process.exit(1);
  });
}

module.exports = { seedData };
