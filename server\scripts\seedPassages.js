const { sequelize } = require('../config/database');
const {
  Passage,
  Badge,
  Porte,
  Personnel,
  AttributionBadge
} = require('../models');

async function seedPassages() {
  try {
    console.log('🌱 Début de l\'insertion des passages de test...');

    // Récupérer les badges et portes existants
    const badges = await Badge.findAll({ limit: 5 });
    const portes = await Porte.findAll();
    
    if (badges.length === 0 || portes.length === 0) {
      console.log('❌ Pas de badges ou portes disponibles. Exécutez d\'abord seedData.js');
      return;
    }

    // Créer des attributions de badges pour avoir des données cohérentes
    const personnel = await Personnel.findAll({ limit: 3 });
    
    for (let i = 0; i < Math.min(badges.length, personnel.length); i++) {
      await AttributionBadge.findOrCreate({
        where: {
          id_badge: badges[i].id,
          id_personnel: personnel[i].id,
          statut: 'actif'
        },
        defaults: {
          date_attribution: new Date(),
          statut: 'actif'
        }
      });
    }

    // Créer des passages de test
    const now = new Date();
    const passages = [];

    // Passages récents (dernière heure)
    for (let i = 0; i < 10; i++) {
      const badge = badges[i % badges.length];
      const porte = portes[i % portes.length];
      const minutesAgo = Math.floor(Math.random() * 60); // 0-60 minutes ago
      
      passages.push({
        id_badge: badge.id,
        id_porte: porte.id,
        date_acces: new Date(now.getTime() - minutesAgo * 60 * 1000),
        type_acces: i % 2 === 0 ? 'entree' : 'sortie',
        resultat: Math.random() > 0.1 ? 'autorise' : 'refuse' // 90% autorisé
      });
    }

    // Passages plus anciens (dernières 24h)
    for (let i = 0; i < 20; i++) {
      const badge = badges[i % badges.length];
      const porte = portes[i % portes.length];
      const hoursAgo = Math.floor(Math.random() * 24); // 0-24 heures ago
      
      passages.push({
        id_badge: badge.id,
        id_porte: porte.id,
        date_acces: new Date(now.getTime() - hoursAgo * 60 * 60 * 1000),
        type_acces: i % 2 === 0 ? 'entree' : 'sortie',
        resultat: Math.random() > 0.05 ? 'autorise' : 'refuse' // 95% autorisé
      });
    }

    // Insérer les passages
    await Passage.bulkCreate(passages, { ignoreDuplicates: true });

    console.log('✅ Passages de test insérés avec succès !');
    console.log(`📊 Résumé :`);
    console.log(`   - ${passages.length} passages créés`);
    console.log(`   - ${badges.length} badges utilisés`);
    console.log(`   - ${portes.length} portes utilisées`);

  } catch (error) {
    console.error('❌ Erreur lors de l\'insertion des passages:', error);
  } finally {
    console.log('🏁 Script terminé');
    process.exit(0);
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  seedPassages();
}

module.exports = seedPassages;
