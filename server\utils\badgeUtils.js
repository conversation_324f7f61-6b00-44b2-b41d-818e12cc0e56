const { Badge, AttributionBadge, TypeBadge } = require('../models');
const { Op } = require('sequelize');

/**
 * Génère un code EPC unique pour un badge
 */
const generateEPCCode = () => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `EPC_${timestamp}_${random}`.toUpperCase();
};

/**
 * Trouve les badges visiteurs disponibles par type
 * @param {string} typeBadge - Type de badge ('badge_visiteur_militaire' ou 'badge_visiteur_civil')
 * @returns {Promise<Array>} Liste des badges disponibles
 */
const getBadgesDisponibles = async (typeBadge) => {
  try {
    // D'abord, récupérer les IDs des badges actuellement attribués
    const attributionsActives = await AttributionBadge.findAll({
      where: { statut: 'actif' },
      attributes: ['id_badge'],
      raw: true
    });

    const badgesAttribues = attributionsActives.map(a => a.id_badge);

    // Ensuite, récupérer les badges disponibles
    const whereClause = {
      permanent: false,
      actif: true
    };

    // Exclure les badges attribués seulement s'il y en a
    if (badgesAttribues.length > 0) {
      whereClause.id = {
        [Op.notIn]: badgesAttribues
      };
    }

    const badges = await Badge.findAll({
      include: [
        {
          model: TypeBadge,
          as: 'typeBadge',
          where: { nom_type_badge: typeBadge }
        }
      ],
      where: whereClause
    });

    return badges;
  } catch (error) {
    console.error('Erreur lors de la récupération des badges disponibles:', error);
    throw error;
  }
};

/**
 * Crée un nouveau badge permanent pour un militaire interne
 * @param {number} idPersonnel - ID du personnel
 * @param {Object} transaction - Transaction Sequelize optionnelle
 * @returns {Promise<Object>} Badge créé et attribution
 */
const creerBadgePermanent = async (idPersonnel, transaction = null) => {
  try {
    // Récupérer le type de badge militaire interne
    const typeBadge = await TypeBadge.findOne({
      where: { nom_type_badge: 'badge_militaire_interne' },
      transaction
    });

    if (!typeBadge) {
      throw new Error('Type de badge militaire interne non trouvé');
    }

    // Créer le badge
    const badge = await Badge.create({
      epc_code: generateEPCCode(),
      id_type_badge: typeBadge.id,
      permanent: true,
      actif: true
    }, { transaction });

    // Créer l'attribution
    const attribution = await AttributionBadge.create({
      id_personnel: idPersonnel,
      id_badge: badge.id,
      statut: 'actif'
    }, { transaction });

    return { badge, attribution };
  } catch (error) {
    console.error('Erreur lors de la création du badge permanent:', error);
    throw error;
  }
};

/**
 * Attribue un badge visiteur à une personne
 * @param {number} idPersonnel - ID du personnel
 * @param {number} idBadge - ID du badge à attribuer
 * @param {Object} transaction - Transaction Sequelize optionnelle
 * @returns {Promise<Object>} Attribution créée
 */
const attribuerBadgeVisiteur = async (idPersonnel, idBadge, transaction = null) => {
  try {
    // Vérifier que le badge est disponible
    const badge = await Badge.findOne({
      where: {
        id: idBadge,
        permanent: false,
        actif: true
      },
      transaction
    });

    if (!badge) {
      throw new Error('Badge non trouvé ou non disponible');
    }

    // Vérifier qu'il n'est pas déjà attribué
    const attributionExistante = await AttributionBadge.findOne({
      where: {
        id_badge: idBadge,
        statut: 'actif'
      },
      transaction
    });

    if (attributionExistante) {
      throw new Error('Badge déjà attribué');
    }

    // Créer l'attribution
    const attribution = await AttributionBadge.create({
      id_personnel: idPersonnel,
      id_badge: idBadge,
      statut: 'actif'
    }, { transaction });
    
    return attribution;
  } catch (error) {
    console.error('Erreur lors de l\'attribution du badge visiteur:', error);
    throw error;
  }
};

/**
 * Clôture une attribution de badge visiteur
 * @param {number} idAttribution - ID de l'attribution à clôturer
 * @returns {Promise<Object>} Attribution mise à jour
 */
const cloturerBadgeVisiteur = async (idAttribution) => {
  try {
    const attribution = await AttributionBadge.findByPk(idAttribution);
    
    if (!attribution) {
      throw new Error('Attribution non trouvée');
    }
    
    if (attribution.statut !== 'actif') {
      throw new Error('Attribution déjà clôturée');
    }
    
    // Mettre à jour l'attribution
    await attribution.update({
      date_fin: new Date(),
      statut: 'expire'
    });
    
    return attribution;
  } catch (error) {
    console.error('Erreur lors de la clôture du badge visiteur:', error);
    throw error;
  }
};

module.exports = {
  generateEPCCode,
  getBadgesDisponibles,
  creerBadgePermanent,
  attribuerBadgeVisiteur,
  cloturerBadgeVisiteur
};
