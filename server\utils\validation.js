const Joi = require('joi');

// Schémas de validation pour les différents types de personnel

const militaireInterneSchema = Joi.object({
  nom: Joi.string().min(2).max(100).required(),
  prenom: Joi.string().min(2).max(100).required(),
  matricule: Joi.string().min(5).max(50).required(),
  id_grade: Joi.number().integer().positive().required(),
  id_unite: Joi.number().integer().positive().required()
});

const militaireExterneSchema = Joi.object({
  nom: Joi.string().min(2).max(100).required(),
  prenom: Joi.string().min(2).max(100).required(),
  matricule: Joi.string().min(5).max(50).required(),
  cin: Joi.string().min(8).max(20).required(),
  id_grade: Joi.number().integer().positive().required(),
  horaire_entree: Joi.date().iso().required(),
  id_unite_origine: Joi.number().integer().positive().required(),
  destination: Joi.number().integer().positive().required(),
  objet_visite: Joi.string().max(500).required(),
  id_badge: Joi.number().integer().positive().required()
});

const civilSchema = Joi.object({
  nom: Joi.string().min(2).max(100).required(),
  prenom: Joi.string().min(2).max(100).required(),
  cin: Joi.string().min(8).max(20).required(),
  societe: Joi.string().max(100).required(),
  horaire_entree: Joi.date().iso().required(),
  destination: Joi.number().integer().positive().required(),
  objet_visite: Joi.string().max(500).required(),
  id_badge: Joi.number().integer().positive().required()
});

const passageSchema = Joi.object({
  epc_code: Joi.string().required(),
  id_porte: Joi.number().integer().positive().required(),
  type_acces: Joi.string().valid('entree', 'sortie').required()
});

const badgeAttributionSchema = Joi.object({
  id_personnel: Joi.number().integer().positive().required(),
  id_badge: Joi.number().integer().positive().required()
});

const badgeCreationSchema = Joi.object({
  type_badge: Joi.string().valid(
    'badge_visiteur_militaire',
    'badge_visiteur_civil'
  ).required()
});

/**
 * Valide les données selon le schéma fourni
 * @param {Object} data - Données à valider
 * @param {Object} schema - Schéma Joi de validation
 * @returns {Object} Résultat de la validation
 */
const validateData = (data, schema) => {
  const { error, value } = schema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
  
  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    
    return {
      isValid: false,
      errors,
      data: null
    };
  }
  
  return {
    isValid: true,
    errors: null,
    data: value
  };
};

/**
 * Middleware de validation pour Express
 * @param {Object} schema - Schéma Joi de validation
 * @returns {Function} Middleware Express
 */
const validationMiddleware = (schema) => {
  return (req, res, next) => {
    const validation = validateData(req.body, schema);
    
    if (!validation.isValid) {
      return res.status(400).json({
        message: 'Données invalides',
        errors: validation.errors
      });
    }
    
    req.validatedData = validation.data;
    next();
  };
};

module.exports = {
  schemas: {
    militaireInterne: militaireInterneSchema,
    militaireExterne: militaireExterneSchema,
    civil: civilSchema,
    passage: passageSchema,
    badgeAttribution: badgeAttributionSchema,
    badgeCreation: badgeCreationSchema
  },
  validateData,
  validationMiddleware
};
