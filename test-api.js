// Test simple pour vérifier si l'API backend fonctionne
const axios = require('axios');

async function testAPI() {
  try {
    console.log('🧪 Test de connexion à l\'API backend...');
    
    // Test de la route de base
    const response = await axios.get('http://localhost:3001/api/personnel');
    console.log('✅ API accessible !');
    console.log(`📊 Données reçues:`, response.data);

    if (response.data.personnel) {
      console.log(`👥 Personnel: ${response.data.personnel.length} éléments`);
    }
    
    // Test CORS
    const corsResponse = await axios.get('http://localhost:3001/api/personnel', {
      headers: {
        'Origin': 'http://localhost:5173'
      }
    });
    console.log('✅ CORS configuré correctement !');
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
    if (error.response) {
      console.error('📄 Statut:', error.response.status);
      console.error('📝 Données:', error.response.data);
    }
  }
}

testAPI();
