/**
 * Script de test complet du système de contrôle d'accès militaire
 * Teste le backend, le frontend et l'intégration complète
 */

const { spawn } = require('child_process');
const axios = require('axios');

// Configuration des couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logHeader(message) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`🎯 ${message}`, 'cyan');
  log(`${'='.repeat(60)}`, 'cyan');
}

async function checkServices() {
  logHeader('VÉRIFICATION DES SERVICES');
  
  let backendOk = false;
  let frontendOk = false;
  
  // Vérifier le backend
  try {
    await axios.get('http://localhost:3001/api/reference/all', { timeout: 5000 });
    logSuccess('Backend accessible sur http://localhost:3001');
    backendOk = true;
  } catch (error) {
    logError('Backend non accessible sur http://localhost:3001');
    logWarning('Assurez-vous que le serveur backend est démarré');
  }
  
  // Vérifier le frontend
  try {
    await axios.get('http://localhost:3000', { timeout: 5000 });
    logSuccess('Frontend accessible sur http://localhost:3000');
    frontendOk = true;
  } catch (error) {
    logError('Frontend non accessible sur http://localhost:3000');
    logWarning('Assurez-vous que le serveur React est démarré');
  }
  
  return { backendOk, frontendOk };
}

function runTest(scriptName, description) {
  return new Promise((resolve, reject) => {
    logInfo(`Exécution: ${description}`);
    
    const child = spawn('node', [scriptName], {
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    let output = '';
    let errorOutput = '';
    
    child.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        logSuccess(`✓ ${description}`);
        console.log(output);
        resolve(true);
      } else {
        logError(`✗ ${description}`);
        console.log(output);
        if (errorOutput) {
          console.error(errorOutput);
        }
        resolve(false);
      }
    });
    
    child.on('error', (error) => {
      logError(`Erreur lors de l'exécution de ${scriptName}: ${error.message}`);
      reject(error);
    });
  });
}

async function runCompleteTests() {
  log('\n🚀 TESTS COMPLETS DU SYSTÈME DE CONTRÔLE D\'ACCÈS MILITAIRE', 'magenta');
  log('================================================================', 'magenta');
  
  // Vérification des services
  const { backendOk, frontendOk } = await checkServices();
  
  if (!backendOk) {
    logError('Le backend doit être démarré pour continuer les tests');
    logInfo('Pour démarrer le backend: npm run server:dev');
    process.exit(1);
  }
  
  let allTestsPassed = true;
  
  // Tests du backend
  logHeader('TESTS DU BACKEND');
  const backendTests = await runTest('test-system.js', 'Tests complets du backend');
  if (!backendTests) {
    allTestsPassed = false;
  }
  
  // Tests du frontend (si disponible)
  if (frontendOk) {
    logHeader('TESTS DU FRONTEND');
    const frontendTests = await runTest('test-frontend.js', 'Tests du frontend React');
    if (!frontendTests) {
      allTestsPassed = false;
    }
  } else {
    logWarning('Tests du frontend ignorés (service non disponible)');
  }
  
  // Résumé final
  logHeader('RÉSUMÉ DES TESTS');
  
  if (allTestsPassed) {
    logSuccess('🎉 TOUS LES TESTS SONT RÉUSSIS !');
    log('\n📋 Système validé:', 'green');
    log('✅ Backend Express.js + PostgreSQL', 'green');
    if (frontendOk) {
      log('✅ Frontend React + Material-UI', 'green');
      log('✅ Intégration Frontend-Backend', 'green');
    }
    log('✅ Gestion du personnel militaire', 'green');
    log('✅ Système de badges RFID', 'green');
    log('✅ Contrôle d\'accès et passages', 'green');
    log('✅ Statistiques et monitoring', 'green');
    
    log('\n🌐 Application disponible:', 'blue');
    log('• Backend API: http://localhost:3001', 'blue');
    if (frontendOk) {
      log('• Frontend Web: http://localhost:3000', 'blue');
    }
    
    log('\n📚 Fonctionnalités testées:', 'cyan');
    log('• Création de militaires internes (badges permanents)', 'cyan');
    log('• Création de militaires externes (badges visiteurs)', 'cyan');
    log('• Création de civils externes (badges visiteurs)', 'cyan');
    log('• Enregistrement des passages RFID', 'cyan');
    log('• Gestion des attributions de badges', 'cyan');
    log('• Statistiques et rapports', 'cyan');
    
  } else {
    logError('💥 CERTAINS TESTS ONT ÉCHOUÉ');
    log('\nVérifiez les erreurs ci-dessus et corrigez les problèmes.', 'red');
  }
  
  return allTestsPassed;
}

// Exécution des tests complets
runCompleteTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  logError(`Erreur inattendue: ${error.message}`);
  process.exit(1);
});
