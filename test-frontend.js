/**
 * Script de test du frontend React
 * Vérifie que l'application React est accessible et fonctionne
 */

const axios = require('axios');

const FRONTEND_URL = 'http://localhost:3000';

// Configuration des couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function testFrontend() {
  log('\n🌐 Test du Frontend React\n', 'blue');
  
  try {
    logInfo('Test: Vérification de l\'accessibilité du frontend');
    
    // Test de base - vérifier que le serveur React répond
    const response = await axios.get(FRONTEND_URL, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Test-Script'
      }
    });
    
    if (response.status === 200) {
      logSuccess('Frontend accessible sur http://localhost:3000');
      
      // Vérifier que c'est bien une application React
      const htmlContent = response.data;
      
      if (htmlContent.includes('react') || htmlContent.includes('React') || htmlContent.includes('root')) {
        logSuccess('Application React détectée');
        
        // Vérifier le titre de l'application
        if (htmlContent.includes('Contrôle d\'Accès') || htmlContent.includes('Control Access')) {
          logSuccess('Titre de l\'application trouvé');
        } else {
          logInfo('Titre spécifique non trouvé, mais l\'application semble fonctionner');
        }
        
        logSuccess('✓ Frontend React fonctionne correctement');
        
      } else {
        logError('Le contenu ne semble pas être une application React');
        return false;
      }
      
    } else {
      logError(`Statut HTTP inattendu: ${response.status}`);
      return false;
    }
    
    return true;
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      logError('Frontend non accessible. Assurez-vous que le serveur React est démarré sur le port 3000');
      logInfo('Pour démarrer le frontend: cd client && npm start');
    } else if (error.code === 'ENOTFOUND') {
      logError('Impossible de résoudre localhost. Problème de réseau.');
    } else if (error.code === 'ETIMEDOUT') {
      logError('Timeout lors de la connexion au frontend');
    } else {
      logError(`Erreur lors du test du frontend: ${error.message}`);
    }
    return false;
  }
}

async function testFrontendIntegration() {
  log('\n🔗 Test d\'intégration Frontend-Backend\n', 'blue');
  
  try {
    logInfo('Test: Vérification que le frontend peut communiquer avec le backend');
    
    // Simuler une requête que le frontend ferait au backend
    // En vérifiant les en-têtes CORS
    const response = await axios.get('http://localhost:3001/api/reference/all', {
      headers: {
        'Origin': 'http://localhost:3000',
        'User-Agent': 'Frontend-Test'
      }
    });
    
    if (response.status === 200) {
      logSuccess('Communication Frontend-Backend fonctionnelle');
      
      // Vérifier les en-têtes CORS
      const corsHeader = response.headers['access-control-allow-origin'];
      if (corsHeader === '*' || corsHeader === 'http://localhost:3000') {
        logSuccess('Configuration CORS correcte');
      } else {
        logInfo('CORS configuré différemment, mais fonctionnel');
      }
      
      return true;
    } else {
      logError('Problème de communication avec le backend');
      return false;
    }
    
  } catch (error) {
    logError(`Erreur de communication Frontend-Backend: ${error.message}`);
    return false;
  }
}

async function runFrontendTests() {
  log('🚀 Début des tests du Frontend\n', 'blue');
  
  const frontendTest = await testFrontend();
  const integrationTest = await testFrontendIntegration();
  
  if (frontendTest && integrationTest) {
    log('\n🎉 Tous les tests frontend sont réussis !', 'green');
    log('\n📋 Résumé:', 'blue');
    log('✅ Frontend React accessible et fonctionnel');
    log('✅ Communication Frontend-Backend opérationnelle');
    log('✅ Configuration CORS correcte');
    log('\n🌐 Application disponible sur: http://localhost:3000', 'blue');
    return true;
  } else {
    log('\n💥 Certains tests frontend ont échoué', 'red');
    return false;
  }
}

// Exécution des tests
runFrontendTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  logError(`Erreur inattendue: ${error.message}`);
  process.exit(1);
});
