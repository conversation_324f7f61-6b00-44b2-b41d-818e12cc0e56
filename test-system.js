/**
 * Script de test complet du système de contrôle d'accès militaire
 * Ce script teste toutes les fonctionnalités principales
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

// Configuration des couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Variables globales pour stocker les données créées
let testData = {
  militaireInterne: null,
  militaireExterne: null,
  civilExterne: null,
  badgeVisiteurMilitaire: null,
  badgeVisiteurCivil: null
};

async function testAPI(description, testFunction) {
  try {
    logInfo(`Test: ${description}`);
    await testFunction();
    logSuccess(`✓ ${description}`);
  } catch (error) {
    logError(`✗ ${description}`);
    console.error('Erreur:', error.response?.data || error.message);
    throw error;
  }
}

async function testReferenceData() {
  await testAPI('Récupération des données de référence', async () => {
    const response = await axios.get(`${API_BASE}/reference/all`);
    
    if (!response.data.grades || response.data.grades.length === 0) {
      throw new Error('Aucun grade trouvé');
    }
    
    if (!response.data.unites || response.data.unites.length === 0) {
      throw new Error('Aucune unité trouvée');
    }
    
    if (!response.data.typesPersonnel || response.data.typesPersonnel.length === 0) {
      throw new Error('Aucun type de personnel trouvé');
    }
    
    logInfo(`Grades: ${response.data.grades.length}, Unités: ${response.data.unites.length}, Types: ${response.data.typesPersonnel.length}`);
  });
}

async function testCreateMilitaireInterne() {
  await testAPI('Création d\'un militaire interne', async () => {
    const timestamp = Date.now();
    const response = await axios.post(`${API_BASE}/personnel/internes`, {
      nom: 'TestNom',
      prenom: 'TestPrenom',
      matricule: `TEST${timestamp}`,
      id_grade: 1,
      id_unite: 1
    });



    if (!response.data.personnel) {
      throw new Error('Personnel non créé');
    }

    if (!response.data.badge) {
      throw new Error('Badge permanent non créé');
    }

    if (!response.data.badge.attribution_id) {
      throw new Error('Attribution non créée');
    }

    testData.militaireInterne = response.data;
    logInfo(`Badge créé: ${response.data.badge.epc_code}`);
  });
}

async function testCreateBadgeVisiteur() {
  await testAPI('Création de badges visiteurs', async () => {
    // Badge visiteur militaire
    const militaireResponse = await axios.post(`${API_BASE}/badges/visiteurs`, {
      type_badge: 'badge_visiteur_militaire'
    });
    
    if (!militaireResponse.data.badge) {
      throw new Error('Badge visiteur militaire non créé');
    }
    
    testData.badgeVisiteurMilitaire = militaireResponse.data.badge;
    logInfo(`Badge visiteur militaire: ${militaireResponse.data.badge.epc_code}`);
    
    // Badge visiteur civil
    const civilResponse = await axios.post(`${API_BASE}/badges/visiteurs`, {
      type_badge: 'badge_visiteur_civil'
    });
    
    if (!civilResponse.data.badge) {
      throw new Error('Badge visiteur civil non créé');
    }
    
    testData.badgeVisiteurCivil = civilResponse.data.badge;
    logInfo(`Badge visiteur civil: ${civilResponse.data.badge.epc_code}`);
  });
}

async function testCreateMilitaireExterne() {
  await testAPI('Création d\'un militaire externe', async () => {
    const timestamp = Date.now();
    const response = await axios.post(`${API_BASE}/personnel/militaires-externes`, {
      nom: 'ExterneNom',
      prenom: 'ExternePrenom',
      matricule: `EXT${timestamp}`,
      cin: `${timestamp}`,
      id_grade: 2,
      horaire_entree: new Date().toISOString(),
      id_unite_origine: 1,
      destination: 1,
      objet_visite: 'Test de visite militaire',
      id_badge: testData.badgeVisiteurMilitaire.id
    });
    
    if (!response.data.personnel) {
      throw new Error('Militaire externe non créé');
    }
    
    if (!response.data.attribution_id) {
      throw new Error('Attribution badge non créée');
    }
    
    testData.militaireExterne = response.data;
    logInfo(`Militaire externe créé avec badge: ${testData.badgeVisiteurMilitaire.epc_code}`);
  });
}

async function testCreateCivilExterne() {
  await testAPI('Création d\'un civil externe', async () => {
    const timestamp = Date.now() + 1; // +1 pour éviter collision
    const response = await axios.post(`${API_BASE}/personnel/civils-externes`, {
      nom: 'CivilNom',
      prenom: 'CivilPrenom',
      cin: `${timestamp}`,
      societe: 'Entreprise Test',
      horaire_entree: new Date().toISOString(),
      destination: 1,
      objet_visite: 'Test de visite civile',
      id_badge: testData.badgeVisiteurCivil.id
    });
    
    if (!response.data.personnel) {
      throw new Error('Civil externe non créé');
    }
    
    if (!response.data.attribution_id) {
      throw new Error('Attribution badge non créée');
    }
    
    testData.civilExterne = response.data;
    logInfo(`Civil externe créé avec badge: ${testData.badgeVisiteurCivil.epc_code}`);
  });
}

async function testPassages() {
  await testAPI('Test des passages RFID', async () => {
    // Test passage autorisé - militaire interne
    const passage1 = await axios.post(`${API_BASE}/passages`, {
      epc_code: testData.militaireInterne.badge.epc_code,
      id_porte: 1,
      type_acces: 'entree'
    });
    
    if (passage1.data.passage.resultat !== 'autorise') {
      throw new Error('Passage militaire interne devrait être autorisé');
    }
    
    // Test passage autorisé - militaire externe
    const passage2 = await axios.post(`${API_BASE}/passages`, {
      epc_code: testData.badgeVisiteurMilitaire.epc_code,
      id_porte: 1,
      type_acces: 'entree'
    });
    
    if (passage2.data.passage.resultat !== 'autorise') {
      throw new Error('Passage militaire externe devrait être autorisé');
    }
    
    // Test passage autorisé - civil externe
    const passage3 = await axios.post(`${API_BASE}/passages`, {
      epc_code: testData.badgeVisiteurCivil.epc_code,
      id_porte: 1,
      type_acces: 'entree'
    });
    
    if (passage3.data.passage.resultat !== 'autorise') {
      throw new Error('Passage civil externe devrait être autorisé');
    }
    
    // Test passage refusé - badge inexistant
    try {
      await axios.post(`${API_BASE}/passages`, {
        epc_code: 'EPC_INEXISTANT',
        id_porte: 1,
        type_acces: 'entree'
      });
      throw new Error('Le passage avec badge inexistant devrait être refusé');
    } catch (error) {
      if (error.response?.status !== 403) {
        throw error;
      }
    }
    
    logInfo('Tous les tests de passage sont réussis');
  });
}

async function testStatistiques() {
  await testAPI('Test des statistiques', async () => {
    // Statistiques badges
    const badgesStats = await axios.get(`${API_BASE}/statistiques/badges`);
    
    if (!badgesStats.data.badges) {
      throw new Error('Statistiques badges manquantes');
    }
    
    // Statistiques passages
    const passagesStats = await axios.get(`${API_BASE}/passages/statistiques?periode=24`);
    
    if (!passagesStats.data.statistiques) {
      throw new Error('Statistiques passages manquantes');
    }
    
    logInfo(`Badges total: ${badgesStats.data.badges.total}, Passages 24h: ${passagesStats.data.statistiques.total}`);
  });
}

async function testBadgeManagement() {
  await testAPI('Test de gestion des badges', async () => {
    // Liste des badges
    const badgesList = await axios.get(`${API_BASE}/badges?page=1&limit=10`);
    
    if (!badgesList.data.badges || badgesList.data.badges.length === 0) {
      throw new Error('Aucun badge trouvé');
    }
    
    // Liste des attributions
    const attributions = await axios.get(`${API_BASE}/badges/attributions/list?statut=actif`);
    
    if (!attributions.data.attributions) {
      throw new Error('Erreur lors de la récupération des attributions');
    }
    
    // Vérification d'accès
    const verification = await axios.get(`${API_BASE}/passages/verifier/${testData.militaireInterne.badge.epc_code}`);
    
    if (!verification.data.acces_autorise) {
      throw new Error('La vérification d\'accès devrait être positive');
    }
    
    logInfo(`Badges trouvés: ${badgesList.data.badges.length}, Attributions actives: ${attributions.data.attributions.length}`);
  });
}

async function runAllTests() {
  log('\n🚀 Début des tests du système de contrôle d\'accès militaire\n', 'blue');
  
  try {
    // Tests des données de référence
    log('\n📋 Tests des données de référence', 'yellow');
    await testReferenceData();
    
    // Tests de création de badges visiteurs
    log('\n🏷️  Tests de création de badges visiteurs', 'yellow');
    await testCreateBadgeVisiteur();
    
    // Tests de création de personnel
    log('\n👥 Tests de création de personnel', 'yellow');
    await testCreateMilitaireInterne();
    await testCreateMilitaireExterne();
    await testCreateCivilExterne();
    
    // Tests des passages
    log('\n🚪 Tests des passages RFID', 'yellow');
    await testPassages();
    
    // Tests des statistiques
    log('\n📊 Tests des statistiques', 'yellow');
    await testStatistiques();
    
    // Tests de gestion des badges
    log('\n🔧 Tests de gestion des badges', 'yellow');
    await testBadgeManagement();
    
    log('\n🎉 Tous les tests sont réussis !', 'green');
    log('\n📋 Résumé des données créées:', 'blue');
    log(`- Militaire interne: ${testData.militaireInterne?.personnel.prenom} ${testData.militaireInterne?.personnel.nom} (${testData.militaireInterne?.badge.epc_code})`);
    log(`- Militaire externe: ${testData.militaireExterne?.personnel.prenom} ${testData.militaireExterne?.personnel.nom} (${testData.badgeVisiteurMilitaire?.epc_code})`);
    log(`- Civil externe: ${testData.civilExterne?.personnel.prenom} ${testData.civilExterne?.personnel.nom} (${testData.badgeVisiteurCivil?.epc_code})`);
    
  } catch (error) {
    log('\n💥 Échec des tests', 'red');
    process.exit(1);
  }
}

// Vérifier que le serveur est démarré
async function checkServer() {
  try {
    await axios.get(`${API_BASE}/reference/all`);
    logSuccess('Serveur backend accessible');
  } catch (error) {
    logError('Serveur backend non accessible. Assurez-vous qu\'il est démarré sur le port 3001');
    process.exit(1);
  }
}

// Exécution des tests
async function main() {
  await checkServer();
  await runAllTests();
}

main().catch(console.error);
